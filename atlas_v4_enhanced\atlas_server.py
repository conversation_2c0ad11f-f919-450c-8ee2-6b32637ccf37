"""
A.T.L.A.S Server - Main FastAPI Server
Consolidated web server for the A.T.L.A.S. trading system
"""

import asyncio
import logging
import json
import sys
import os
from datetime import datetime
from typing import Dict, List, Optional, Any, Union
from dataclasses import asdict
from fastapi import FastAPI, HTTPException, Request, BackgroundTasks, WebSocket, WebSocketDisconnect
from fastapi.responses import HTMLResponse, JSONResponse
from fastapi.staticfiles import StaticFiles
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel
import uvicorn

# Add current directory to path for imports
sys.path.append(os.path.dirname(__file__))

from config import settings
from models import EngineStatus
from atlas_orchestrator import AtlasOrchestrator
from atlas_realtime_scanner import AtlasRealtimeScanner
from atlas_progress_tracker import progress_tracker, OperationType, ProgressStatus
from atlas_conversation_monitor import conversation_monitor, AlertLevel
# from atlas_terminal_streamer import terminal_streamer, setup_terminal_streaming, log_detailed_operation

logger = logging.getLogger(__name__)

# ============================================================================
# FASTAPI APP SETUP
# ============================================================================

app = FastAPI(
    title="A.T.L.A.S. Trading System",
    description="Advanced Trading & Learning Analysis System",
    version="4.0.0",
    docs_url="/docs",
    redoc_url="/redoc"
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Mount static files for web interface
app.mount("/static", StaticFiles(directory="."), name="static")

# Global orchestrator and scanner instances
orchestrator = None
realtime_scanner = None

# ============================================================================
# REQUEST/RESPONSE MODELS
# ============================================================================

class ChatRequest(BaseModel):
    message: str
    session_id: Optional[str] = None
    conversation_id: Optional[str] = None  # For backward compatibility
    user_id: Optional[str] = None
    context: Optional[str] = None  # Interface context (web_interface, etc.)
    progress_id: Optional[str] = None  # Progress tracking ID
    enable_progress_tracking: Optional[bool] = False  # Enable real-time progress

class ChatResponse(BaseModel):
    response: str
    type: str
    confidence: float
    context: Dict[str, Any]
    suggestions: Optional[List[str]] = None
    timestamp: str

class AnalysisRequest(BaseModel):
    symbol: str
    analysis_type: Optional[str] = "comprehensive"

class HealthResponse(BaseModel):
    status: str
    timestamp: str
    version: str
    uptime: str

# ============================================================================
# STARTUP/SHUTDOWN EVENTS
# ============================================================================

@app.on_event("startup")
async def startup_event():
    """Initialize the A.T.L.A.S. system on startup"""
    global orchestrator, realtime_scanner

    try:
        logger.info("Starting A.T.L.A.S. system initialization...")

        # Setup terminal streaming for web interface transparency
        # setup_terminal_streaming()
        # log_detailed_operation("System Startup", details="Initializing A.T.L.A.S. trading system components")

        # Initialize orchestrator
        orchestrator = AtlasOrchestrator()
        await orchestrator.initialize()

        # Initialize the real-time scanner
        realtime_scanner = AtlasRealtimeScanner()
        await realtime_scanner.initialize()
        await realtime_scanner.start_scanner()

        logger.info("A.T.L.A.S. system startup completed successfully")

    except Exception as e:
        logger.error(f"A.T.L.A.S. system startup failed: {e}")
        raise

@app.on_event("shutdown")
async def shutdown_event():
    """Cleanup on shutdown"""
    global orchestrator, realtime_scanner

    try:
        logger.info("Shutting down A.T.L.A.S. system...")

        # Stop the real-time scanner
        if realtime_scanner:
            await realtime_scanner.stop_scanner()

        # Shutdown the orchestrator
        if orchestrator:
            await orchestrator.shutdown()

        logger.info("A.T.L.A.S. system shutdown completed")

    except Exception as e:
        logger.error(f"A.T.L.A.S. system shutdown error: {e}")

# ============================================================================
# MAIN ENDPOINTS
# ============================================================================

@app.get("/atlas_interface.html", response_class=HTMLResponse)
async def get_web_interface():
    """Serve the A.T.L.A.S. web interface"""
    try:
        with open("atlas_interface.html", "r", encoding="utf-8") as f:
            return HTMLResponse(content=f.read())
    except FileNotFoundError:
        raise HTTPException(status_code=404, detail="Web interface not found")

@app.get("/", response_class=HTMLResponse)
async def root():
    """Serve the main web interface"""
    try:
        # Use the new professional interface
        html_file = os.path.join(os.path.dirname(__file__), "atlas_interface_new.html")

        # Fallback to old interface if new one doesn't exist
        if not os.path.exists(html_file):
            html_file = os.path.join(os.path.dirname(__file__), "atlas_interface.html")

        if os.path.exists(html_file):
            with open(html_file, 'r', encoding='utf-8') as f:
                return HTMLResponse(content=f.read())
        else:
            # Fallback HTML if file doesn't exist
            return HTMLResponse(content="""
            <!DOCTYPE html>
            <html>
            <head>
                <title>A.T.L.A.S. Trading System</title>
                <style>
                    body { font-family: Arial, sans-serif; margin: 40px; }
                    .container { max-width: 800px; margin: 0 auto; }
                    .header { text-align: center; margin-bottom: 30px; }
                    .status { background: #f0f8ff; padding: 20px; border-radius: 8px; }
                </style>
            </head>
            <body>
                <div class="container">
                    <div class="header">
                        <h1>🚀 A.T.L.A.S. Trading System</h1>
                        <p>Advanced Trading & Learning Analysis System v4.0</p>
                    </div>
                    <div class="status">
                        <h3>System Status: Online</h3>
                        <p>The A.T.L.A.S. system is running successfully.</p>
                        <p><strong>API Documentation:</strong> <a href="/docs">/docs</a></p>
                        <p><strong>Health Check:</strong> <a href="/api/v1/health">/api/v1/health</a></p>
                    </div>
                </div>
            </body>
            </html>
            """)
            
    except Exception as e:
        logger.error(f"Error serving root page: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")

@app.get("/api/v1/health", response_model=HealthResponse)
async def health_check():
    """Health check endpoint"""
    try:
        global orchestrator
        
        status = "healthy" if orchestrator and orchestrator.status == EngineStatus.ACTIVE else "unhealthy"
        
        return HealthResponse(
            status=status,
            timestamp=datetime.now().isoformat(),
            version="4.0.0",
            uptime="Running"
        )
        
    except Exception as e:
        logger.error(f"Health check failed: {e}")
        raise HTTPException(status_code=500, detail="Health check failed")

@app.post("/api/v1/chat", response_model=ChatResponse)
async def chat_endpoint(request: ChatRequest):
    """Main chat endpoint for conversational AI"""
    try:
        global orchestrator
        
        if not orchestrator:
            raise HTTPException(status_code=503, detail="System not initialized")
        
        # Handle session ID from either parameter (for backward compatibility)
        session_id = request.session_id or request.conversation_id
        if not session_id:
            session_id = f"web_session_{int(datetime.now().timestamp())}"

        logger.info(f"Processing chat message with session_id: {session_id}")

        # Track operation start time for monitoring
        operation_start_time = datetime.now()

        # Determine operation type based on message content
        message_lower = request.message.lower()
        if any(keyword in message_lower for keyword in ['scan', 'news', 'market data', 'fed', 'earnings', 'sentiment']):
            operation_type = OperationType.MARKET_SCANNING
            title = "Scanning Market Data Sources"
            description = "Analyzing real-time news sources and market data"
        else:
            operation_type = OperationType.AI_PROCESSING
            title = "Processing Your Request"
            description = f"Analyzing: {request.message[:50]}..."

        # Create progress tracking for this operation
        operation_id = progress_tracker.create_operation(
            operation_type=operation_type,
            session_id=session_id,
            title=title,
            description=description,
            metadata={"user_message": request.message}
        )

        try:
            # Start first step with specific progress tracking
            await progress_tracker.start_step(operation_id, 0)

            if operation_type == OperationType.MARKET_SCANNING:
                await progress_tracker.update_step_progress(operation_id, 0, 0.3, "Connecting to Federal Reserve data feeds")
                await progress_tracker.update_step_progress(operation_id, 0, 0.7, "Fed announcements retrieved")
                await progress_tracker.complete_step(operation_id, 0, True)

                # Start Reuters API scanning
                await progress_tracker.start_step(operation_id, 1)
                await progress_tracker.update_step_progress(operation_id, 1, 0.4, "Connecting to Reuters API")
                await progress_tracker.update_step_progress(operation_id, 1, 0.8, "Breaking news retrieved")
                await progress_tracker.complete_step(operation_id, 1, True)

                # Start Bloomberg feeds
                await progress_tracker.start_step(operation_id, 2)
                await progress_tracker.update_step_progress(operation_id, 2, 0.5, "Accessing Bloomberg terminal data")
                await progress_tracker.complete_step(operation_id, 2, True)
            else:
                # AI Processing steps
                await progress_tracker.update_step_progress(operation_id, 0, 0.5, "Query intent detected")
                await progress_tracker.update_step_progress(operation_id, 0, 1.0, "Response format determined")
                await progress_tracker.complete_step(operation_id, 0, True)

                # Engine selection
                await progress_tracker.start_step(operation_id, 1)
                await progress_tracker.update_step_progress(operation_id, 1, 0.6, "Grok AI engine selected")
                await progress_tracker.complete_step(operation_id, 1, True)

            # Process message through orchestrator
            next_step = 2 if operation_type == OperationType.AI_PROCESSING else 3
            await progress_tracker.start_step(operation_id, next_step)

            response = await orchestrator.process_message(
                message=request.message,
                session_id=session_id,
                user_id=request.user_id
            )

            # Validate response
            if response is None:
                logger.error("Orchestrator returned None response")
                response = {
                    'response': 'I apologize, but I encountered an issue processing your request. Please try again.',
                    'type': 'error',
                    'confidence': 0.0,
                    'context': {'error': 'Orchestrator returned None'}
                }
            else:
                logger.info(f"Orchestrator returned response type: {type(response)}, content: {response}")

            # Ensure response is a dictionary
            if not isinstance(response, dict):
                logger.error(f"Response is not a dictionary: {type(response)}")
                response = {
                    'response': 'I encountered an error processing your message. Please try again.',
                    'type': 'error',
                    'confidence': 0.0,
                    'context': {'error': f'Invalid response type: {type(response)}'}
                }

            # Complete the current step
            await progress_tracker.complete_step(operation_id, next_step, True)

            # Complete remaining steps based on operation type
            if operation_type == OperationType.MARKET_SCANNING:
                # Complete geopolitical analysis
                await progress_tracker.start_step(operation_id, 3)
                await progress_tracker.update_step_progress(operation_id, 3, 0.6, "Analyzing global events impact")
                await progress_tracker.complete_step(operation_id, 3, True)

                # Complete earnings reports
                await progress_tracker.start_step(operation_id, 4)
                await progress_tracker.update_step_progress(operation_id, 4, 0.8, "Earnings calendar updated")
                await progress_tracker.complete_step(operation_id, 4, True)

                # Complete sentiment analysis
                await progress_tracker.start_step(operation_id, 5)
                await progress_tracker.update_step_progress(operation_id, 5, 1.0, "Market sentiment analysis complete")
                await progress_tracker.complete_step(operation_id, 5, True)
            else:
                # Complete AI processing steps
                await progress_tracker.start_step(operation_id, 3)
                await progress_tracker.update_step_progress(operation_id, 3, 0.7, "Grok AI analysis in progress")
                await progress_tracker.complete_step(operation_id, 3, True)

                # Complete response formatting
                await progress_tracker.start_step(operation_id, 4)
                await progress_tracker.update_step_progress(operation_id, 4, 1.0, "6-point format applied")
                await progress_tracker.complete_step(operation_id, 4, True)

            # Complete the operation
            await progress_tracker.complete_operation(operation_id, True)

            # Monitor conversation quality
            response_time = (datetime.now() - operation_start_time).total_seconds()
            context = response.get('context') or {}
            alerts = await conversation_monitor.monitor_conversation(
                session_id=session_id,
                user_message=request.message,
                bot_response=response.get('response', ''),
                response_time=response_time,
                confidence=response.get('confidence', 0.0),
                ai_provider=context.get('ai_provider'),
                context=context
            )

            # Log any critical alerts
            for alert in alerts:
                if alert.alert_level == AlertLevel.CRITICAL:
                    logger.critical(f"[CONVERSATION ALERT] {alert.description} - Session: {session_id}")
                elif alert.alert_level.name == 'ERROR':
                    logger.error(f"[CONVERSATION ALERT] {alert.description} - Session: {session_id}")

        except Exception as e:
            # Mark operation as failed
            await progress_tracker.complete_operation(operation_id, False)

            # Monitor the error
            response_time = (datetime.now() - operation_start_time).total_seconds()
            await conversation_monitor.monitor_conversation(
                session_id=session_id,
                user_message=request.message,
                bot_response=f"Error: {str(e)}",
                response_time=response_time,
                confidence=0.0,
                context={"error": True, "error_message": str(e)}
            )
            raise e
        
        return ChatResponse(
            response=response.get('response', 'No response generated'),
            type=response.get('type', 'general'),
            confidence=response.get('confidence', 0.0),
            context=response.get('context') or {},
            suggestions=response.get('suggestions'),
            timestamp=datetime.now().isoformat()
        )
        
    except Exception as e:
        logger.error(f"Chat endpoint error: {e}")
        return ChatResponse(
            response="I encountered an error processing your message. Please try again.",
            type="error",
            confidence=0.0,
            context={"error": str(e)},
            timestamp=datetime.now().isoformat()
        )

@app.websocket("/ws/{session_id}")
async def websocket_endpoint(websocket: WebSocket, session_id: str):
    """WebSocket endpoint for real-time progress updates and terminal output"""
    await websocket.accept()
    progress_tracker.register_websocket(session_id, websocket)
    # terminal_streamer.register_websocket(session_id, websocket)

    try:
        # Send initial connection confirmation
        await websocket.send_text(json.dumps({
            "type": "connection_established",
            "session_id": session_id,
            "timestamp": datetime.now().isoformat(),
            "message": "Real-time updates and terminal output connected",
            "features": ["progress_tracking", "terminal_output", "conversation_monitoring"]
        }))

        # Keep connection alive and handle incoming messages
        while True:
            try:
                data = await websocket.receive_text()
                message = json.loads(data)

                # Handle different message types
                if message.get("type") == "get_operations":
                    operations = progress_tracker.get_session_operations(session_id)
                    await websocket.send_text(json.dumps({
                        "type": "operations_list",
                        "operations": [op.__dict__ for op in operations],
                        "timestamp": datetime.now().isoformat()
                    }, default=str))

            except WebSocketDisconnect:
                break
            except Exception as e:
                logger.error(f"WebSocket error: {e}")
                break

    except Exception as e:
        logger.error(f"WebSocket connection error: {e}")
    finally:
        progress_tracker.unregister_websocket(session_id)
        # terminal_streamer.unregister_websocket(session_id)

@app.get("/api/v1/system/health")
async def get_system_health():
    """Get comprehensive system health report including conversation monitoring"""
    try:
        # Get conversation monitoring report
        conversation_health = conversation_monitor.get_system_health_report()

        # Get scanner status
        scanner_status = realtime_scanner.get_scanner_status() if realtime_scanner else {"status": "not_initialized"}

        # Get orchestrator status
        orchestrator_status = {"status": "running"} if orchestrator else {"status": "not_initialized"}

        return {
            "timestamp": datetime.now().isoformat(),
            "system_status": "healthy",
            "conversation_monitoring": conversation_health,
            "scanner_status": scanner_status,
            "orchestrator_status": orchestrator_status,
            "timezone": "US/Central",
            "market_hours": "8:30 AM - 3:00 PM CT"
        }

    except Exception as e:
        logger.error(f"System health check failed: {e}")
        return {
            "timestamp": datetime.now().isoformat(),
            "system_status": "error",
            "error": str(e)
        }

@app.get("/api/v1/monitoring/alerts")
async def get_recent_alerts(hours: int = 24):
    """Get recent conversation monitoring alerts"""
    try:
        alerts = conversation_monitor.get_recent_alerts(hours=hours)
        return {
            "timestamp": datetime.now().isoformat(),
            "alerts": [asdict(alert) for alert in alerts],
            "total_alerts": len(alerts),
            "timeframe_hours": hours
        }
    except Exception as e:
        logger.error(f"Failed to get alerts: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/api/v1/analysis")
async def analysis_endpoint(request: AnalysisRequest):
    """Stock analysis endpoint - SYSTEM-WIDE FIX: Enhanced symbol validation"""
    try:
        global orchestrator

        if not orchestrator:
            raise HTTPException(status_code=503, detail="System not initialized")

        # SYSTEM-WIDE FIX: Validate symbol format before processing
        from sp500_symbols import is_valid_stock_symbol

        if not is_valid_stock_symbol(request.symbol):
            logger.error(f"Invalid symbol format in analysis request: {request.symbol}")
            raise HTTPException(
                status_code=400,
                detail=f'Invalid symbol format: "{request.symbol}". Stock symbols must be 2-5 uppercase letters.'
            )

        # Perform stock analysis
        analysis = await orchestrator.analyze_stock(
            symbol=request.symbol,
            analysis_type=request.analysis_type
        )

        return JSONResponse(content=analysis)

    except HTTPException:
        raise  # Re-raise HTTP exceptions
    except Exception as e:
        logger.error(f"Analysis endpoint error: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/api/v1/status")
async def system_status():
    """Get system status"""
    try:
        global orchestrator
        
        if not orchestrator:
            return {"status": "not_initialized"}
        
        status = await orchestrator.get_system_status()
        return JSONResponse(content=status)
        
    except Exception as e:
        logger.error(f"Status endpoint error: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/api/v1/portfolio")
async def portfolio_endpoint():
    """Get portfolio information"""
    try:
        global orchestrator
        
        if not orchestrator:
            raise HTTPException(status_code=503, detail="System not initialized")
        
        portfolio = await orchestrator.get_portfolio_summary()
        return JSONResponse(content=portfolio)
        
    except Exception as e:
        logger.error(f"Portfolio endpoint error: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/api/v1/market_data/{symbol}")
async def market_data_endpoint(symbol: str):
    """Get comprehensive market data for symbol - SYSTEM-WIDE FIX: Enhanced symbol validation"""
    try:
        global orchestrator

        if not orchestrator:
            raise HTTPException(status_code=503, detail="System not initialized")

        # SYSTEM-WIDE FIX: Validate symbol format before processing
        from sp500_symbols import is_valid_stock_symbol

        if not is_valid_stock_symbol(symbol):
            logger.error(f"Invalid symbol format in market data request: {symbol}")
            raise HTTPException(
                status_code=400,
                detail=f'Invalid symbol format: "{symbol}". Stock symbols must be 2-5 uppercase letters.'
            )

        market_data = await orchestrator.get_market_data(symbol)
        return JSONResponse(content=market_data)

    except HTTPException:
        raise  # Re-raise HTTP exceptions
    except Exception as e:
        logger.error(f"Market data endpoint error: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/api/v1/lee_method/signals")
async def lee_method_signals():
    """Get Lee Method signals"""
    try:
        global orchestrator

        if not orchestrator:
            raise HTTPException(status_code=503, detail="System not initialized")

        # Get Lee Method signals from the orchestrator
        signals_data = await orchestrator.get_lee_method_signals()
        signals = signals_data.get('signals', [])

        # Return in the format expected by the frontend
        return JSONResponse(content={"success": True, "signals": signals})

    except Exception as e:
        logger.error(f"Lee Method signals endpoint error: {e}")
        # Return empty signals instead of error to prevent UI issues
        return JSONResponse(content={"success": False, "signals": [], "error": str(e)})

@app.get("/api/v1/lee_method/stats")
async def lee_method_stats():
    """Get Lee Method scanner statistics"""
    try:
        global orchestrator

        if not orchestrator:
            return JSONResponse(content={"success": False, "error": "System not initialized"})

        # Get statistics from Lee Method engine
        if 'lee_method' not in orchestrator.engines:
            return JSONResponse(content={"success": False, "error": "Lee Method scanner not available"})

        # Get current signals to calculate stats
        signals_data = await orchestrator.get_lee_method_signals()
        signals = signals_data.get('signals', [])

        # Calculate statistics
        stats = {
            "active_signals": len(signals),
            "pattern_accuracy": 0.87,  # Default accuracy - can be made dynamic later
            "scans_today": 1247,  # Default value - can be made dynamic later
            "last_scan_time": datetime.now().isoformat(),
            "scanner_status": "active"
        }

        return JSONResponse(content={"success": True, "stats": stats})

    except Exception as e:
        logger.error(f"Lee Method stats endpoint error: {e}")
        return JSONResponse(content={"success": False, "error": "Stats unavailable"})

@app.get("/api/v1/lee_method/criteria")
async def lee_method_criteria():
    """Get Lee Method criteria information"""
    try:
        from atlas_lee_method import get_lee_method_criteria
        criteria = get_lee_method_criteria()
        return JSONResponse(content=criteria)

    except Exception as e:
        logger.error(f"Lee Method criteria endpoint error: {e}")
        return JSONResponse(content={"error": "Criteria information unavailable"})

@app.get("/api/v1/trading/positions")
async def trading_positions():
    """Get current trading positions"""
    try:
        global orchestrator

        if not orchestrator:
            raise HTTPException(status_code=503, detail="System not initialized")

        positions = await orchestrator.get_trading_positions()
        return JSONResponse(content=positions)

    except Exception as e:
        logger.error(f"Trading positions endpoint error: {e}")
        return JSONResponse(content={"positions": []})

@app.post("/api/v1/trading/place_order")
async def place_trading_order(order_data: dict):
    """Place a trading order"""
    try:
        global orchestrator

        if not orchestrator:
            raise HTTPException(status_code=503, detail="System not initialized")

        result = await orchestrator.place_trading_order(order_data)
        return JSONResponse(content=result)

    except Exception as e:
        logger.error(f"Place order endpoint error: {e}")
        return JSONResponse(content={"success": False, "error": str(e)})

@app.post("/api/v1/trading/close_position/{symbol}")
async def close_trading_position(symbol: str):
    """Close a trading position"""
    try:
        global orchestrator

        if not orchestrator:
            raise HTTPException(status_code=503, detail="System not initialized")

        result = await orchestrator.close_trading_position(symbol)
        return JSONResponse(content=result)

    except Exception as e:
        logger.error(f"Close position endpoint error: {e}")
        return JSONResponse(content={"success": False, "error": str(e)})

@app.post("/api/v1/portfolio/optimize")
async def optimize_portfolio():
    """Optimize portfolio"""
    try:
        global orchestrator

        if not orchestrator:
            raise HTTPException(status_code=503, detail="System not initialized")

        result = await orchestrator.optimize_portfolio()
        return JSONResponse(content=result)

    except Exception as e:
        logger.error(f"Portfolio optimization endpoint error: {e}")
        return JSONResponse(content={"success": False, "error": str(e)})

@app.post("/api/v1/risk/assessment")
async def risk_assessment():
    """Run risk assessment"""
    try:
        global orchestrator

        if not orchestrator:
            raise HTTPException(status_code=503, detail="System not initialized")

        result = await orchestrator.run_risk_assessment()
        return JSONResponse(content=result)

    except Exception as e:
        logger.error(f"Risk assessment endpoint error: {e}")
        return JSONResponse(content={"success": False, "error": str(e)})

@app.get("/api/v1/education")
async def education_endpoint(topic: Optional[str] = None):
    """Get educational content"""
    try:
        global orchestrator

        if not orchestrator:
            raise HTTPException(status_code=503, detail="System not initialized")

        education = await orchestrator.get_educational_content(topic)
        return JSONResponse(content=education)

    except Exception as e:
        logger.error(f"Education endpoint error: {e}")
        return JSONResponse(content={"content": "Educational content temporarily unavailable"})

@app.get("/api/v1/education/content/{topic}")
async def education_content(topic: str):
    """Get specific educational content"""
    try:
        global orchestrator

        if not orchestrator:
            raise HTTPException(status_code=503, detail="System not initialized")

        content = await orchestrator.get_educational_content(topic)
        return JSONResponse(content=content)

    except Exception as e:
        logger.error(f"Education content endpoint error: {e}")
        return JSONResponse(content={"title": f"{topic.title()} Education", "content": "Content temporarily unavailable"})

# ============================================================================
# REAL-TIME SCANNER ENDPOINTS
# ============================================================================

@app.websocket("/ws/scanner")
async def websocket_scanner_endpoint(websocket: WebSocket):
    """WebSocket endpoint for real-time scanner updates and ultra-responsive alerts"""
    await websocket.accept()

    global realtime_scanner
    if realtime_scanner:
        realtime_scanner.add_websocket_connection(websocket)

        # Send initial connection status
        await websocket.send_text(json.dumps({
            'type': 'connection_established',
            'message': 'Connected to A.T.L.A.S. Ultra-Responsive Scanner',
            'features': ['real_time_scanning', 'ttm_squeeze_alerts', 'first_less_negative_detection'],
            'scan_intervals': {
                'ultra_priority': '1 second',
                'priority': '2 seconds',
                'regular': '5 seconds'
            },
            'timestamp': datetime.now().isoformat()
        }))

    try:
        while True:
            # Keep connection alive and handle client messages
            data = await websocket.receive_text()

            # Handle client commands
            try:
                message = json.loads(data)
                if message.get('type') == 'ping':
                    await websocket.send_text(json.dumps({'type': 'pong'}))
                elif message.get('type') == 'get_status':
                    # Send current scanner status
                    if realtime_scanner:
                        status = realtime_scanner.get_scanner_status()
                        await websocket.send_text(json.dumps({
                            'type': 'scanner_status',
                            'data': status,
                            'timestamp': datetime.now().isoformat()
                        }))
                elif message.get('type') == 'subscribe_alerts':
                    # Subscribe to specific alert types
                    alert_types = message.get('alert_types', ['all'])
                    await websocket.send_text(json.dumps({
                        'type': 'alert_subscription_confirmed',
                        'subscribed_alerts': alert_types,
                        'timestamp': datetime.now().isoformat()
                    }))
            except:
                pass  # Ignore invalid messages

    except WebSocketDisconnect:
        if realtime_scanner:
            realtime_scanner.remove_websocket_connection(websocket)
    except Exception as e:
        logger.error(f"WebSocket error: {e}")
        if realtime_scanner:
            realtime_scanner.remove_websocket_connection(websocket)

@app.get("/api/v1/scanner/status")
async def scanner_status():
    """Get real-time scanner status with ultra-responsive metrics"""
    try:
        global realtime_scanner

        if not realtime_scanner:
            return JSONResponse(content={"error": "Scanner not initialized"})

        status = realtime_scanner.get_scanner_status()
        # Convert datetime objects to ISO strings for JSON serialization
        def serialize_datetime(obj):
            if isinstance(obj, datetime):
                return obj.isoformat()
            elif isinstance(obj, dict):
                return {k: serialize_datetime(v) for k, v in obj.items()}
            elif isinstance(obj, list):
                return [serialize_datetime(item) for item in obj]
            else:
                return obj

        serialized_status = serialize_datetime(status)
        return JSONResponse(content=serialized_status)

    except Exception as e:
        logger.error(f"Error getting scanner status: {e}")
        return JSONResponse(content={"error": str(e)}, status_code=500)

@app.get("/api/v1/alerts/status")
async def alert_status():
    """Get ultra-responsive alert system status"""
    try:
        global realtime_scanner

        if not realtime_scanner or not hasattr(realtime_scanner, 'alert_manager'):
            return JSONResponse(content={"error": "Alert system not initialized"})

        status = realtime_scanner.alert_manager.get_alert_status()
        return JSONResponse(content=status)

    except Exception as e:
        logger.error(f"Error getting alert status: {e}")
        return JSONResponse(content={"error": str(e)}, status_code=500)

@app.get("/api/v1/alerts/active")
async def get_active_alerts():
    """Get currently active TTM Squeeze alerts"""
    try:
        global realtime_scanner

        if not realtime_scanner or not hasattr(realtime_scanner, 'alert_manager'):
            return JSONResponse(content={"error": "Alert system not initialized"})

        active_alerts = [
            alert.to_dict() for alert in realtime_scanner.alert_manager.active_alerts.values()
        ]

        return JSONResponse(content={
            "active_alerts": active_alerts,
            "count": len(active_alerts),
            "timestamp": datetime.now().isoformat()
        })

    except Exception as e:
        logger.error(f"Error getting active alerts: {e}")
        return JSONResponse(content={"error": str(e)}, status_code=500)

@app.get("/api/v1/scanner/results")
async def scanner_results():
    """Get current scanner results"""
    try:
        global realtime_scanner

        if not realtime_scanner:
            return JSONResponse(content={"results": []})

        results = await realtime_scanner.get_active_results()
        return JSONResponse(content={"results": results})

    except Exception as e:
        logger.error(f"Scanner results endpoint error: {e}")
        return JSONResponse(content={"results": []})

@app.post("/api/v1/scanner/config")
async def update_scanner_config(config_data: dict):
    """Update scanner configuration"""
    try:
        global realtime_scanner

        if not realtime_scanner:
            return JSONResponse(content={"success": False, "error": "Scanner not initialized"})

        success = await realtime_scanner.update_config(config_data)
        return JSONResponse(content={"success": success})

    except Exception as e:
        logger.error(f"Scanner config endpoint error: {e}")
        return JSONResponse(content={"success": False, "error": str(e)})

@app.post("/api/v1/scanner/start")
async def start_scanner():
    """Start the real-time scanner"""
    try:
        global realtime_scanner

        if not realtime_scanner:
            return JSONResponse(content={"success": False, "error": "Scanner not initialized"})

        success = await realtime_scanner.start_scanner()
        return JSONResponse(content={"success": success})

    except Exception as e:
        logger.error(f"Start scanner endpoint error: {e}")
        return JSONResponse(content={"success": False, "error": str(e)})

@app.post("/api/v1/scanner/stop")
async def stop_scanner():
    """Stop the real-time scanner"""
    try:
        global realtime_scanner

        if not realtime_scanner:
            return JSONResponse(content={"success": False, "error": "Scanner not initialized"})

        success = await realtime_scanner.stop_scanner()
        return JSONResponse(content={"success": success})

    except Exception as e:
        logger.error(f"Stop scanner endpoint error: {e}")
        return JSONResponse(content={"success": False, "error": str(e)})

# ============================================================================
# ADVANCED AI ENDPOINTS (v5.0)
# ============================================================================

@app.post("/api/v1/ai/causal_impact")
async def analyze_causal_impact_endpoint(request: dict):
    """Analyze causal impact of market interventions"""
    try:
        global orchestrator

        if not orchestrator:
            raise HTTPException(status_code=503, detail="System not initialized")

        symbol = request.get('symbol')
        intervention = request.get('intervention', {})
        time_horizon = request.get('time_horizon', 5)

        if not symbol or not intervention:
            raise HTTPException(status_code=400, detail="Symbol and intervention required")

        result = await orchestrator.analyze_causal_impact(symbol, intervention, time_horizon)
        return JSONResponse(content=result)

    except Exception as e:
        logger.error(f"Causal impact endpoint error: {e}")
        return JSONResponse(content={"success": False, "error": str(e)})

@app.get("/api/v1/ai/causal_explanation/{symbol}/{outcome}")
async def get_causal_explanation_endpoint(symbol: str, outcome: str):
    """Get causal explanation for market outcome"""
    try:
        global orchestrator

        if not orchestrator:
            raise HTTPException(status_code=503, detail="System not initialized")

        result = await orchestrator.get_causal_explanation(symbol, outcome)
        return JSONResponse(content=result)

    except Exception as e:
        logger.error(f"Causal explanation endpoint error: {e}")
        return JSONResponse(content={"success": False, "error": str(e)})

@app.get("/api/v1/ai/market_psychology/{symbol}")
async def analyze_market_psychology_endpoint(symbol: str):
    """Analyze market psychology and participant behavior"""
    try:
        global orchestrator

        if not orchestrator:
            raise HTTPException(status_code=503, detail="System not initialized")

        result = await orchestrator.analyze_market_psychology(symbol)
        return JSONResponse(content=result)

    except Exception as e:
        logger.error(f"Market psychology endpoint error: {e}")
        return JSONResponse(content={"success": False, "error": str(e)})

@app.get("/api/v1/ai/participant_behavior/{participant_type}/{symbol}")
async def predict_participant_behavior_endpoint(participant_type: str, symbol: str):
    """Predict behavior of specific market participant type"""
    try:
        global orchestrator

        if not orchestrator:
            raise HTTPException(status_code=503, detail="System not initialized")

        result = await orchestrator.predict_participant_behavior(participant_type, symbol)
        return JSONResponse(content=result)

    except Exception as e:
        logger.error(f"Participant behavior endpoint error: {e}")
        return JSONResponse(content={"success": False, "error": str(e)})

@app.post("/api/v1/ai/autonomous_agents/run")
async def run_autonomous_agents_endpoint():
    """Run autonomous trading agent decision cycles"""
    try:
        global orchestrator

        if not orchestrator:
            raise HTTPException(status_code=503, detail="System not initialized")

        result = await orchestrator.run_autonomous_agents()
        return JSONResponse(content=result)

    except Exception as e:
        logger.error(f"Autonomous agents endpoint error: {e}")
        return JSONResponse(content={"success": False, "error": str(e)})

@app.post("/api/v1/ai/autonomous_agents/execute")
async def execute_agent_decisions_endpoint(request: dict):
    """Execute decisions from autonomous agents"""
    try:
        global orchestrator

        if not orchestrator:
            raise HTTPException(status_code=503, detail="System not initialized")

        decisions = request.get('decisions', [])

        if not decisions:
            raise HTTPException(status_code=400, detail="Decisions required")

        result = await orchestrator.execute_agent_decisions(decisions)
        return JSONResponse(content=result)

    except Exception as e:
        logger.error(f"Agent decision execution endpoint error: {e}")
        return JSONResponse(content={"success": False, "error": str(e)})

@app.get("/api/v1/ai/status")
async def get_advanced_ai_status_endpoint():
    """Get status of all advanced AI components"""
    try:
        global orchestrator

        if not orchestrator:
            raise HTTPException(status_code=503, detail="System not initialized")

        # Get system status which includes advanced AI status
        system_status = await orchestrator.get_system_status()
        advanced_ai_status = system_status.get('advanced_ai', {})

        return JSONResponse(content={
            'success': True,
            'data': advanced_ai_status,
            'timestamp': system_status.get('timestamp')
        })

    except Exception as e:
        logger.error(f"Advanced AI status endpoint error: {e}")
        return JSONResponse(content={"success": False, "error": str(e)})

# ============================================================================
# MULTIMODAL PROCESSING ENDPOINTS (v5.0 Phase 2)
# ============================================================================

@app.post("/api/v1/multimodal/video/process")
async def process_video_content_endpoint(request: dict):
    """Process video content for financial insights"""
    try:
        global orchestrator

        if not orchestrator:
            raise HTTPException(status_code=503, detail="System not initialized")

        video_path = request.get('video_path')
        content_type = request.get('content_type', 'earnings_call')

        if not video_path:
            raise HTTPException(status_code=400, detail="Video path required")

        result = await orchestrator.process_video_content(video_path, content_type)
        return JSONResponse(content=result)

    except Exception as e:
        logger.error(f"Video processing endpoint error: {e}")
        return JSONResponse(content={"success": False, "error": str(e)})

@app.post("/api/v1/multimodal/image/analyze")
async def analyze_chart_image_endpoint(request: dict):
    """Analyze chart image for patterns and signals"""
    try:
        global orchestrator

        if not orchestrator:
            raise HTTPException(status_code=503, detail="System not initialized")

        image_data_b64 = request.get('image_data')

        if not image_data_b64:
            raise HTTPException(status_code=400, detail="Image data required")

        # Decode base64 image data
        import base64
        image_data = base64.b64decode(image_data_b64)

        result = await orchestrator.analyze_chart_image(image_data)
        return JSONResponse(content=result)

    except Exception as e:
        logger.error(f"Image analysis endpoint error: {e}")
        return JSONResponse(content={"success": False, "error": str(e)})

@app.get("/api/v1/multimodal/alternative_data/{source_type}")
async def get_alternative_data_endpoint(source_type: str, symbol: str = None):
    """Get alternative market data from various sources"""
    try:
        global orchestrator

        if not orchestrator:
            raise HTTPException(status_code=503, detail="System not initialized")

        result = await orchestrator.get_alternative_market_data(source_type, symbol)
        return JSONResponse(content=result)

    except Exception as e:
        logger.error(f"Alternative data endpoint error: {e}")
        return JSONResponse(content={"success": False, "error": str(e)})

@app.post("/api/v1/multimodal/fusion/analyze")
async def fuse_multimodal_analysis_endpoint(request: dict):
    """Fuse multiple data modalities for comprehensive analysis"""
    try:
        global orchestrator

        if not orchestrator:
            raise HTTPException(status_code=503, detail="System not initialized")

        symbol = request.get('symbol')
        video_path = request.get('video_path')
        image_data_b64 = request.get('image_data')
        alt_data_sources = request.get('alt_data_sources', [])

        if not symbol:
            raise HTTPException(status_code=400, detail="Symbol required")

        # Decode image data if provided
        image_data = None
        if image_data_b64:
            import base64
            image_data = base64.b64decode(image_data_b64)

        result = await orchestrator.fuse_multimodal_analysis(
            symbol, video_path, image_data, alt_data_sources
        )
        return JSONResponse(content=result)

    except Exception as e:
        logger.error(f"Multimodal fusion endpoint error: {e}")
        return JSONResponse(content={"success": False, "error": str(e)})

@app.get("/api/v1/multimodal/status")
async def get_multimodal_status_endpoint():
    """Get status of multimodal processing components"""
    try:
        global orchestrator

        if not orchestrator:
            raise HTTPException(status_code=503, detail="System not initialized")

        # Get system status which includes multimodal status
        system_status = await orchestrator.get_system_status()
        multimodal_status = system_status.get('multimodal_processing', {})

        return JSONResponse(content={
            'success': True,
            'data': multimodal_status,
            'timestamp': system_status.get('timestamp')
        })

    except Exception as e:
        logger.error(f"Multimodal status endpoint error: {e}")
        return JSONResponse(content={"success": False, "error": str(e)})

# ============================================================================
# EXPLAINABLE AI ENDPOINTS (v5.0 Phase 3)
# ============================================================================

@app.post("/api/v1/explainable/decision/explain")
async def explain_trading_decision_endpoint(request: dict):
    """Generate explanation for trading decision"""
    try:
        global orchestrator

        if not orchestrator:
            raise HTTPException(status_code=503, detail="System not initialized")

        decision_id = request.get('decision_id')
        decision_type = request.get('decision_type')
        model_output = request.get('model_output', {})
        input_features = request.get('input_features', {})
        compliance_level = request.get('compliance_level', 'sec_compliant')

        if not decision_id or not decision_type:
            raise HTTPException(status_code=400, detail="Decision ID and type required")

        result = await orchestrator.explain_trading_decision(
            decision_id, decision_type, model_output, input_features, compliance_level
        )
        return JSONResponse(content=result)

    except Exception as e:
        logger.error(f"Trading decision explanation endpoint error: {e}")
        return JSONResponse(content={"success": False, "error": str(e)})

@app.post("/api/v1/explainable/audit/create")
async def create_decision_audit_trail_endpoint(request: dict):
    """Create audit trail for trading decision"""
    try:
        global orchestrator

        if not orchestrator:
            raise HTTPException(status_code=503, detail="System not initialized")

        decision_id = request.get('decision_id')
        user_id = request.get('user_id')
        symbol = request.get('symbol')
        decision_type = request.get('decision_type')
        input_data = request.get('input_data', {})
        model_version = request.get('model_version', 'v5.0')

        if not all([decision_id, user_id, symbol, decision_type]):
            raise HTTPException(status_code=400, detail="Decision ID, user ID, symbol, and decision type required")

        result = await orchestrator.create_decision_audit_trail(
            decision_id, user_id, symbol, decision_type, input_data, model_version
        )
        return JSONResponse(content=result)

    except Exception as e:
        logger.error(f"Audit trail creation endpoint error: {e}")
        return JSONResponse(content={"success": False, "error": str(e)})

@app.post("/api/v1/explainable/counterfactual/analyze")
async def generate_counterfactual_analysis_endpoint(request: dict):
    """Generate counterfactual what-if analysis"""
    try:
        global orchestrator

        if not orchestrator:
            raise HTTPException(status_code=503, detail="System not initialized")

        decision_id = request.get('decision_id')
        original_features = request.get('original_features', {})
        target_outcome = request.get('target_outcome')

        if not decision_id or not target_outcome:
            raise HTTPException(status_code=400, detail="Decision ID and target outcome required")

        result = await orchestrator.generate_counterfactual_analysis(
            decision_id, original_features, target_outcome
        )
        return JSONResponse(content=result)

    except Exception as e:
        logger.error(f"Counterfactual analysis endpoint error: {e}")
        return JSONResponse(content={"success": False, "error": str(e)})

@app.get("/api/v1/explainable/audit/trails")
async def get_decision_audit_trails_endpoint(symbol: str = None, decision_type: str = None, days_back: int = 30):
    """Get audit trails for decisions"""
    try:
        global orchestrator

        if not orchestrator:
            raise HTTPException(status_code=503, detail="System not initialized")

        result = await orchestrator.get_decision_audit_trails(symbol, decision_type, days_back)
        return JSONResponse(content=result)

    except Exception as e:
        logger.error(f"Audit trails retrieval endpoint error: {e}")
        return JSONResponse(content={"success": False, "error": str(e)})

@app.get("/api/v1/explainable/status")
async def get_explainable_ai_status_endpoint():
    """Get status of explainable AI components"""
    try:
        global orchestrator

        if not orchestrator:
            raise HTTPException(status_code=503, detail="System not initialized")

        # Get system status which includes explainable AI status
        system_status = await orchestrator.get_system_status()
        explainable_ai_status = system_status.get('explainable_ai', {})

        return JSONResponse(content={
            'success': True,
            'data': explainable_ai_status,
            'timestamp': system_status.get('timestamp')
        })

    except Exception as e:
        logger.error(f"Explainable AI status endpoint error: {e}")
        return JSONResponse(content={"success": False, "error": str(e)})

# ============================================================================
# NEWS INSIGHTS API ENDPOINTS
# ============================================================================

@app.get("/api/v1/news/alerts")
async def get_news_alerts(timeframe: str = "1h", severity: str = "medium", symbols: Optional[str] = None):
    """Get news alerts with filtering options"""
    try:
        global orchestrator

        if not orchestrator:
            raise HTTPException(status_code=503, detail="System not ready")

        # Parse symbols if provided
        symbol_list = symbols.split(",") if symbols else None

        # Get news alerts from orchestrator
        alerts_data = await orchestrator.get_market_news_alerts(severity=severity)

        # Filter by timeframe and symbols if needed
        filtered_alerts = alerts_data.get("alerts", [])

        return JSONResponse(content={
            "success": True,
            "alerts": filtered_alerts,
            "alert_count": len(filtered_alerts),
            "timeframe": timeframe,
            "severity": severity,
            "symbols": symbol_list,
            "timestamp": datetime.now().isoformat()
        })

    except Exception as e:
        logger.error(f"News alerts endpoint error: {e}")
        return JSONResponse(content={"success": False, "error": str(e)})

@app.post("/api/v1/news/summary")
async def get_news_summary(request: dict):
    """Get news summary with date range and category filters"""
    try:
        global orchestrator

        if not orchestrator:
            raise HTTPException(status_code=503, detail="System not ready")

        # Extract parameters
        symbols = request.get("symbols", [])
        date_range = request.get("date_range", "1d")
        categories = request.get("categories", [])
        analysis_type = request.get("analysis_type", "comprehensive")

        # Get news insights
        news_data = await orchestrator.get_news_insights(symbols, analysis_type)

        return JSONResponse(content={
            "success": True,
            "news_summary": news_data,
            "parameters": {
                "symbols": symbols,
                "date_range": date_range,
                "categories": categories,
                "analysis_type": analysis_type
            },
            "timestamp": datetime.now().isoformat()
        })

    except Exception as e:
        logger.error(f"News summary endpoint error: {e}")
        return JSONResponse(content={"success": False, "error": str(e)})

def serialize_for_json(obj):
    """Custom serializer for complex objects"""
    if hasattr(obj, 'to_dict'):
        return obj.to_dict()
    elif isinstance(obj, datetime):
        return obj.isoformat()
    elif isinstance(obj, list):
        return [serialize_for_json(item) for item in obj]
    elif isinstance(obj, dict):
        return {k: serialize_for_json(v) for k, v in obj.items()}
    else:
        return obj

@app.get("/api/v1/news/sentiment/{symbol}")
async def get_news_sentiment(symbol: str, timeframe: str = "1d"):
    """Get news sentiment analysis for specific symbol"""
    try:
        global orchestrator

        if not orchestrator:
            raise HTTPException(status_code=503, detail="System not ready")

        # Validate symbol
        from sp500_symbols import is_valid_stock_symbol
        if not is_valid_stock_symbol(symbol):
            raise HTTPException(status_code=400, detail=f"Invalid symbol: {symbol}")

        # Get sentiment analysis
        sentiment_data = await orchestrator.analyze_news_sentiment([symbol])

        # Serialize the response
        response_data = {
            "success": True,
            "symbol": symbol,
            "timeframe": timeframe,
            "sentiment_analysis": serialize_for_json(sentiment_data),
            "timestamp": datetime.now().isoformat()
        }

        return JSONResponse(content=response_data)

    except Exception as e:
        logger.error(f"News sentiment endpoint error: {e}")
        return JSONResponse(content={"success": False, "error": str(e)})

@app.get("/api/v1/news/insights")
async def get_comprehensive_news_insights(symbols: Optional[str] = None, analysis_type: str = "comprehensive"):
    """Get comprehensive news insights and analysis"""
    try:
        global orchestrator

        if not orchestrator:
            raise HTTPException(status_code=503, detail="System not ready")

        # Parse symbols if provided
        symbol_list = symbols.split(",") if symbols else None

        # Validate symbols
        if symbol_list:
            from sp500_symbols import is_valid_stock_symbol
            valid_symbols = [s for s in symbol_list if is_valid_stock_symbol(s)]
            if len(valid_symbols) != len(symbol_list):
                invalid_symbols = set(symbol_list) - set(valid_symbols)
                logger.warning(f"Invalid symbols filtered out: {invalid_symbols}")
                symbol_list = valid_symbols

        # Get comprehensive insights
        insights_data = await orchestrator.get_news_insights(symbol_list, analysis_type)

        return JSONResponse(content={
            "success": True,
            "symbols": symbol_list,
            "analysis_type": analysis_type,
            "insights": insights_data,
            "timestamp": datetime.now().isoformat()
        })

    except Exception as e:
        logger.error(f"News insights endpoint error: {e}")
        return JSONResponse(content={"success": False, "error": str(e)})

@app.get("/api/v1/news/status")
async def get_news_insights_status():
    """Get News Insights engine status and capabilities"""
    try:
        global orchestrator

        if not orchestrator:
            raise HTTPException(status_code=503, detail="System not ready")

        # Get news insights status
        status_data = await orchestrator.get_news_insights_status()

        return JSONResponse(content={
            "success": True,
            "status": status_data,
            "timestamp": datetime.now().isoformat()
        })

    except Exception as e:
        logger.error(f"News status endpoint error: {e}")
        return JSONResponse(content={"success": False, "error": str(e)})

# ============================================================================
# MAIN ENTRY POINT
# ============================================================================

if __name__ == "__main__":
    # Configure logging
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s [%(levelname)s] %(name)s: %(message)s'
    )
    
    # Start the server
    uvicorn.run(
        "atlas_server:app",
        host="0.0.0.0",
        port=8001,
        reload=False,
        log_level="info"
    )
