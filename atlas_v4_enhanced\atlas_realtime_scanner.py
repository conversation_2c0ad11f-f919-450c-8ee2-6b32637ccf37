"""
A.T.L.A.S. Real-Time Scanner Engine
Continuous monitoring of S&P 500 stocks for Lee Method pattern detection
Provides real-time updates via WebSocket and efficient API usage management
"""

import asyncio
import logging
import json
import sys
import os
import time as time_module
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Set
from dataclasses import dataclass, asdict
import pandas as pd
import numpy as np
from concurrent.futures import ThreadPoolExecutor
import threading
import queue
import time as time_module
import pytz

# Add helper tools to path
sys.path.append(os.path.join(os.path.dirname(__file__), '4_helper_tools'))

from config import get_api_config, settings
from models import Quote, EngineStatus, TTMSqueezeSignal, SignalStrength
from sp500_symbols import get_sp500_symbols, get_core_sp500_symbols, get_high_volume_symbols
from atlas_lee_method import LeeMethodScanner
from atlas_market_core import AtlasMarketEngine
from atlas_alert_manager import alert_manager, AlertType, AlertPriority
from atlas_performance_monitor import performance_monitor, PerformanceMetric

logger = logging.getLogger(__name__)

@dataclass
class ScannerResult:
    """Real-time scanner result"""
    symbol: str
    price: float
    change: float
    change_percent: float
    pattern_found: bool
    pattern_type: str
    confidence: float
    signal_strength: str
    timestamp: str
    histogram_current: float
    ema5_trend: bool
    ema8_trend: bool
    momentum_trend: bool
    squeeze_active: bool
    target_price: Optional[float] = None
    stop_loss: Optional[float] = None

@dataclass
class ScannerConfig:
    """Ultra-responsive scanner configuration"""
    enabled: bool = True
    scan_interval: int = 5  # seconds - ULTRA-RESPONSIVE: Every 5 seconds
    market_hours_only: bool = True
    symbol_filter: List[str] = None  # None = all S&P 500
    min_confidence: float = 0.4  # Lower threshold for faster detection
    max_concurrent_scans: int = 8  # ULTRA-RESPONSIVE: Increased concurrent processing
    api_rate_limit: int = 100  # ULTRA-RESPONSIVE: Increased rate limit
    require_squeeze: bool = False
    pattern_sensitivity: float = 0.7
    priority_symbols: List[str] = None  # High-volume symbols for priority scanning
    priority_scan_interval: int = 2  # seconds - ULTRA-FAST for priority symbols
    ultra_priority_scan_interval: int = 1  # seconds - For critical patterns
    batch_size: int = 10  # symbols per batch - Optimized batch processing
    enable_parallel_processing: bool = True
    max_worker_threads: int = 6  # Parallel processing threads
    cache_duration: int = 15  # seconds - Brief cache for efficiency
    enable_predictive_scanning: bool = True  # Scan symbols showing early momentum

class AtlasRealtimeScanner:
    """
    Continuous real-time scanner for Lee Method pattern detection
    Monitors all S&P 500 stocks during market hours with efficient API usage
    """
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.config = ScannerConfig()
        self.lee_scanner = LeeMethodScanner()
        self.market_engine = AtlasMarketEngine()

        # Scanner state
        self.is_running = False
        self.is_market_hours = False
        self.scan_count = 0
        self.last_scan_time = None
        self.active_results: Dict[str, ScannerResult] = {}
        self.scan_queue = queue.Queue()
        self.result_queue = queue.Queue()
        
        # Threading
        self.scanner_thread = None
        self.result_processor_thread = None
        self.executor = ThreadPoolExecutor(max_workers=self.config.max_concurrent_scans)
        
        # API rate limiting
        self.api_calls_per_minute = 0
        self.last_api_reset = datetime.now()
        self.api_lock = threading.Lock()
        
        # WebSocket connections for real-time updates
        self.websocket_connections: Set = set()

        # Ultra-responsive alert system
        self.alert_manager = alert_manager

        # Performance monitoring system
        self.performance_monitor = performance_monitor

        # Ultra-responsive scanning optimization
        self.symbol_priority_cache = {}  # Cache symbol priorities for faster access
        self.market_data_cache = {}  # Brief cache for market data
        self.last_cache_update = {}  # Track cache timestamps
        self.ultra_priority_symbols = set()  # Symbols requiring ultra-fast scanning
        self.momentum_tracking = {}  # Track momentum changes for predictive scanning

        # Performance optimization
        self.parallel_executor = None  # Will be initialized when scanner starts
        self.scanning_queues = {
            'ultra_priority': queue.Queue(),
            'priority': queue.Queue(),
            'regular': queue.Queue()
        }
        
        # Market hours (8:30 AM - 3:00 PM CT / 9:30 AM - 4:00 PM ET)
        # Using Central Time for Houston, Texas
        from datetime import time as dt_time
        self.market_open = dt_time(8, 30)  # 8:30 AM CT
        self.market_close = dt_time(15, 0)  # 3:00 PM CT
        
        self.logger.info("[OK] A.T.L.A.S. Real-Time Scanner initialized")

    async def initialize(self) -> bool:
        """Initialize the scanner engine"""
        try:
            # Initialize market engine first
            await self.market_engine.initialize()

            # Initialize Lee Method scanner
            await self.lee_scanner.initialize()

            # Load scanner configuration
            await self._load_scanner_config()

            # Get symbols to scan with prioritization (OPTIMIZED for faster scanning)
            if self.config.symbol_filter:
                self.scan_symbols = self.config.symbol_filter[:30]  # Increased from 10 to 30 symbols
            else:
                # Use prioritized symbol approach for optimal performance
                high_volume_symbols = get_high_volume_symbols()
                core_symbols = get_core_sp500_symbols()

                # Priority symbols: Top 15 high-volume symbols for fastest scanning
                self.priority_symbols = high_volume_symbols[:15]
                # Regular symbols: Next 15 core symbols for normal scanning
                self.regular_symbols = [s for s in core_symbols[:30] if s not in self.priority_symbols][:15]

                self.scan_symbols = self.priority_symbols + self.regular_symbols
                self.config.priority_symbols = self.priority_symbols

            self.logger.info(f"[OK] Scanner initialized with {len(self.scan_symbols)} symbols")
            return True

        except Exception as e:
            self.logger.error(f"Scanner initialization failed: {e}")
            return False

    async def start_scanner(self) -> bool:
        """Start the ultra-responsive continuous real-time scanner"""
        try:
            if self.is_running:
                self.logger.warning("Scanner is already running")
                return True

            self.is_running = True
            self.logger.info("[SCANNER] Starting ultra-responsive real-time scanner...")

            # Initialize parallel processing if enabled
            if self.config.enable_parallel_processing:
                self.parallel_executor = ThreadPoolExecutor(max_workers=self.config.max_worker_threads)
                self.logger.info(f"[SCANNER] Parallel processing enabled with {self.config.max_worker_threads} workers")

            # Initialize symbol priorities for ultra-responsive scanning
            await self._initialize_symbol_priorities()

            # Start performance monitoring
            self.performance_monitor.start_monitoring()

            # Start ultra-responsive scanner thread
            self.scanner_thread = threading.Thread(target=self._ultra_responsive_scanner_loop, daemon=True)
            self.scanner_thread.start()

            # Start result processor thread
            self.result_processor_thread = threading.Thread(target=self._result_processor_loop, daemon=True)
            self.result_processor_thread.start()

            self.logger.info("[SCANNER] Ultra-responsive real-time scanner started successfully")
            return True

        except Exception as e:
            self.logger.error(f"Failed to start scanner: {e}")
            self.is_running = False
            return False

    async def stop_scanner(self) -> bool:
        """Stop the continuous real-time scanner"""
        try:
            self.is_running = False
            
            # Wait for threads to finish
            if self.scanner_thread and self.scanner_thread.is_alive():
                self.scanner_thread.join(timeout=5)
            
            if self.result_processor_thread and self.result_processor_thread.is_alive():
                self.result_processor_thread.join(timeout=5)
            
            # Shutdown executor
            self.executor.shutdown(wait=True)
            
            self.logger.info("[OK] Real-time scanner stopped")
            return True
            
        except Exception as e:
            self.logger.error(f"Error stopping scanner: {e}")
            return False

    def _scanner_loop(self):
        """Main scanner loop running in background thread with priority-based scanning"""
        # Set up event loop for this thread
        try:
            loop = asyncio.get_event_loop()
        except RuntimeError:
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)

        priority_last_scan = 0
        regular_last_scan = 0

        while self.is_running:
            try:
                current_time = time_module.time()

                # Check if we should scan (market hours, enabled, etc.)
                if not self._should_scan():
                    time_module.sleep(5)  # Check again in 5 seconds (reduced from 10)
                    continue

                # Priority symbols scanning (faster interval)
                if (current_time - priority_last_scan) >= self.config.priority_scan_interval:
                    self._perform_priority_scan_cycle()
                    priority_last_scan = current_time

                # Regular symbols scanning (normal interval)
                if (current_time - regular_last_scan) >= self.config.scan_interval:
                    self._perform_regular_scan_cycle()
                    regular_last_scan = current_time

                # Short sleep to prevent CPU overload
                time_module.sleep(2)  # Reduced from scan_interval to 2 seconds

            except Exception as e:
                self.logger.error(f"Error in scanner loop: {e}")
                time_module.sleep(10)  # Reduced retry wait from 30 to 10 seconds

        # Clean up the event loop when thread exits
        if loop and not loop.is_closed():
            loop.close()

    async def _initialize_symbol_priorities(self):
        """Initialize symbol priorities for ultra-responsive scanning"""
        try:
            # Get high-volume symbols for priority scanning
            high_volume_symbols = get_high_volume_symbols()
            self.priority_symbols = high_volume_symbols[:20]  # Top 20 high-volume

            # Initialize ultra-priority symbols (will be populated dynamically)
            self.ultra_priority_symbols = set()

            # Initialize momentum tracking
            for symbol in self.scan_symbols:
                self.momentum_tracking[symbol] = {
                    'last_histogram': None,
                    'momentum_direction': 'neutral',
                    'consecutive_improvements': 0,
                    'last_update': datetime.now()
                }

            self.logger.info(f"[SCANNER] Initialized priorities: {len(self.priority_symbols)} priority symbols")

        except Exception as e:
            self.logger.error(f"Error initializing symbol priorities: {e}")

    def _ultra_responsive_scanner_loop(self):
        """Ultra-responsive scanner loop with multi-tier scanning"""
        try:
            loop = asyncio.get_event_loop()
        except RuntimeError:
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)

        last_ultra_scan = 0
        last_priority_scan = 0
        last_regular_scan = 0

        while self.is_running:
            try:
                current_time = time_module.time()

                # Check if we should scan
                if not self._should_scan():
                    time_module.sleep(1)
                    continue

                # Ultra-priority scanning (1 second interval)
                if current_time - last_ultra_scan >= self.config.ultra_priority_scan_interval:
                    if self.ultra_priority_symbols:
                        loop.run_until_complete(self._perform_ultra_priority_scan())
                    last_ultra_scan = current_time

                # Priority scanning (2 second interval)
                if current_time - last_priority_scan >= self.config.priority_scan_interval:
                    loop.run_until_complete(self._perform_priority_scan_optimized())
                    last_priority_scan = current_time

                # Regular scanning (5 second interval)
                if current_time - last_regular_scan >= self.config.scan_interval:
                    loop.run_until_complete(self._perform_regular_scan_optimized())
                    last_regular_scan = current_time

                # Brief sleep to prevent CPU overload
                time_module.sleep(0.1)

            except Exception as e:
                self.logger.error(f"Error in ultra-responsive scanner loop: {e}")
                time_module.sleep(1)

        # Clean up
        if loop and not loop.is_closed():
            loop.close()

    async def _perform_ultra_priority_scan(self):
        """Scan ultra-priority symbols with maximum responsiveness"""
        try:
            if not self.ultra_priority_symbols:
                return

            symbols_to_scan = list(self.ultra_priority_symbols)[:5]  # Limit to 5 for speed
            scan_start_time = time_module.time()

            # Start progress tracking
            self.performance_monitor.start_scan_progress(len(symbols_to_scan))

            patterns_detected = 0
            alerts_generated = 0

            # Use parallel processing for maximum speed
            if self.config.enable_parallel_processing and self.parallel_executor:
                tasks = []
                for symbol in symbols_to_scan:
                    task = asyncio.create_task(self._scan_symbol_ultra_fast(symbol))
                    tasks.append(task)

                results = await asyncio.gather(*tasks, return_exceptions=True)

                for i, result in enumerate(results):
                    if isinstance(result, ScannerResult) and result.pattern_found:
                        self.result_queue.put(result)
                        patterns_detected += 1
                        alerts_generated += 1

                        # Record signal performance
                        self.performance_monitor.record_signal_performance(
                            result.symbol, result.confidence
                        )

                    # Update progress
                    self.performance_monitor.update_scan_progress(
                        i + 1, symbols_to_scan[i] if i < len(symbols_to_scan) else "",
                        patterns_detected, alerts_generated
                    )
            else:
                # Sequential scanning for ultra-priority
                for i, symbol in enumerate(symbols_to_scan):
                    result = await self._scan_symbol_ultra_fast(symbol)
                    if result and result.pattern_found:
                        self.result_queue.put(result)
                        patterns_detected += 1
                        alerts_generated += 1

                        # Record signal performance
                        self.performance_monitor.record_signal_performance(
                            result.symbol, result.confidence
                        )

                    # Update progress
                    self.performance_monitor.update_scan_progress(
                        i + 1, symbol, patterns_detected, alerts_generated
                    )

            # Complete progress tracking
            self.performance_monitor.complete_scan_progress()

            # Record performance metrics
            scan_duration = time_module.time() - scan_start_time
            self.performance_monitor.record_metric(
                PerformanceMetric.SCAN_LATENCY, scan_duration
            )
            self.performance_monitor.record_metric(
                PerformanceMetric.SYMBOLS_PER_SECOND, len(symbols_to_scan) / scan_duration
            )

            self.logger.debug(f"[ULTRA] Scanned {len(symbols_to_scan)} ultra-priority symbols in {scan_duration:.2f}s")

        except Exception as e:
            self.logger.error(f"Error in ultra-priority scan: {e}")
            self.performance_monitor.complete_scan_progress()

    async def _perform_priority_scan_optimized(self):
        """Optimized priority symbol scanning"""
        try:
            if not hasattr(self, 'priority_symbols') or not self.priority_symbols:
                return

            # Use cached data when possible
            symbols_to_scan = self.priority_symbols[:10]  # Limit for speed

            if self.config.enable_parallel_processing and self.parallel_executor:
                # Parallel processing
                tasks = [self._scan_symbol_with_cache(symbol) for symbol in symbols_to_scan]
                results = await asyncio.gather(*tasks, return_exceptions=True)

                for result in results:
                    if isinstance(result, ScannerResult) and result.pattern_found:
                        self.result_queue.put(result)
                        # Promote to ultra-priority if strong signal
                        if result.confidence >= 0.8:
                            self.ultra_priority_symbols.add(result.symbol)
            else:
                # Sequential scanning
                for symbol in symbols_to_scan:
                    result = await self._scan_symbol_with_cache(symbol)
                    if result and result.pattern_found:
                        self.result_queue.put(result)
                        if result.confidence >= 0.8:
                            self.ultra_priority_symbols.add(result.symbol)

            self.logger.debug(f"[PRIORITY] Scanned {len(symbols_to_scan)} priority symbols")

        except Exception as e:
            self.logger.error(f"Error in priority scan: {e}")

    async def _perform_regular_scan_optimized(self):
        """Optimized regular symbol scanning"""
        try:
            regular_symbols = [s for s in self.scan_symbols if s not in self.priority_symbols]

            # Batch processing for efficiency
            batch_size = self.config.batch_size
            for i in range(0, len(regular_symbols), batch_size):
                batch = regular_symbols[i:i + batch_size]

                if self.config.enable_parallel_processing and self.parallel_executor:
                    tasks = [self._scan_symbol_with_cache(symbol) for symbol in batch]
                    results = await asyncio.gather(*tasks, return_exceptions=True)

                    for result in results:
                        if isinstance(result, ScannerResult) and result.pattern_found:
                            self.result_queue.put(result)
                            # Promote promising symbols
                            if result.confidence >= 0.7:
                                if result.symbol not in self.priority_symbols:
                                    self.priority_symbols.append(result.symbol)
                else:
                    # Sequential processing
                    for symbol in batch:
                        result = await self._scan_symbol_with_cache(symbol)
                        if result and result.pattern_found:
                            self.result_queue.put(result)
                            if result.confidence >= 0.7:
                                if result.symbol not in self.priority_symbols:
                                    self.priority_symbols.append(result.symbol)

                # Brief pause between batches
                await asyncio.sleep(0.1)

            self.logger.debug(f"[REGULAR] Scanned {len(regular_symbols)} regular symbols")

        except Exception as e:
            self.logger.error(f"Error in regular scan: {e}")

    async def _scan_symbol_ultra_fast(self, symbol: str) -> Optional[ScannerResult]:
        """Ultra-fast symbol scanning with minimal latency"""
        try:
            # Check cache first for ultra-fast response
            if symbol in self.market_data_cache:
                cache_time = self.last_cache_update.get(symbol, datetime.min)
                if (datetime.now() - cache_time).total_seconds() < 5:  # 5-second cache
                    cached_data = self.market_data_cache[symbol]
                    # Use cached market data with fresh pattern detection
                    return await self._scan_with_cached_data(symbol, cached_data)

            # Fresh scan for ultra-priority symbols
            return await self._scan_single_symbol(symbol)

        except Exception as e:
            self.logger.error(f"Error in ultra-fast scan for {symbol}: {e}")
            return None

    async def _scan_symbol_with_cache(self, symbol: str) -> Optional[ScannerResult]:
        """Scan symbol with intelligent caching for performance"""
        try:
            # Check if we have recent cached data
            if symbol in self.market_data_cache:
                cache_time = self.last_cache_update.get(symbol, datetime.min)
                cache_age = (datetime.now() - cache_time).total_seconds()

                if cache_age < self.config.cache_duration:
                    # Use cached data
                    cached_data = self.market_data_cache[symbol]
                    return await self._scan_with_cached_data(symbol, cached_data)

            # Perform fresh scan and cache results
            result = await self._scan_single_symbol(symbol)

            if result:
                # Cache the market data for future use
                self.market_data_cache[symbol] = {
                    'price': result.price,
                    'change': result.change,
                    'change_percent': result.change_percent,
                    'timestamp': result.timestamp
                }
                self.last_cache_update[symbol] = datetime.now()

            return result

        except Exception as e:
            self.logger.error(f"Error in cached scan for {symbol}: {e}")
            return None

    async def _scan_with_cached_data(self, symbol: str, cached_data: Dict[str, Any]) -> Optional[ScannerResult]:
        """Perform pattern detection using cached market data"""
        try:
            # Get fresh pattern detection (this is the critical part)
            signal = await self.lee_method_scanner.scan_symbol(symbol)

            if not signal or signal.confidence < self.config.min_confidence:
                return None

            # Create result using cached market data
            result = ScannerResult(
                symbol=symbol,
                price=cached_data.get('price', 0.0),
                change=cached_data.get('change', 0.0),
                change_percent=cached_data.get('change_percent', 0.0),
                pattern_found=True,
                pattern_type='lee_method_ttm_squeeze',
                confidence=signal.confidence,
                signal_strength=signal.strength if signal.strength else 'MODERATE',
                timestamp=datetime.now().isoformat(),
                histogram_current=signal.histogram_current,
                ema5_trend=signal.ema5_uptrend,
                ema8_trend=signal.ema8_uptrend,
                momentum_trend=signal.momentum_uptrend,
                squeeze_active=signal.squeeze_active,
                target_price=signal.target_price,
                stop_loss=signal.stop_loss
            )

            # Update momentum tracking for predictive scanning
            self._update_momentum_tracking(symbol, signal)

            return result

        except Exception as e:
            self.logger.error(f"Error scanning with cached data for {symbol}: {e}")
            return None

    def _update_momentum_tracking(self, symbol: str, signal):
        """Update momentum tracking for predictive scanning"""
        try:
            if symbol not in self.momentum_tracking:
                self.momentum_tracking[symbol] = {
                    'last_histogram': None,
                    'momentum_direction': 'neutral',
                    'consecutive_improvements': 0,
                    'last_update': datetime.now()
                }

            tracking = self.momentum_tracking[symbol]
            current_hist = signal.histogram_current

            if tracking['last_histogram'] is not None:
                if current_hist > tracking['last_histogram']:
                    # Histogram improving
                    if tracking['momentum_direction'] == 'improving':
                        tracking['consecutive_improvements'] += 1
                    else:
                        tracking['momentum_direction'] = 'improving'
                        tracking['consecutive_improvements'] = 1

                    # Promote to ultra-priority if showing strong momentum
                    if tracking['consecutive_improvements'] >= 2:
                        self.ultra_priority_symbols.add(symbol)

                elif current_hist < tracking['last_histogram']:
                    # Histogram declining
                    tracking['momentum_direction'] = 'declining'
                    tracking['consecutive_improvements'] = 0

                    # Remove from ultra-priority if momentum turns negative
                    self.ultra_priority_symbols.discard(symbol)

            tracking['last_histogram'] = current_hist
            tracking['last_update'] = datetime.now()

        except Exception as e:
            self.logger.error(f"Error updating momentum tracking for {symbol}: {e}")

    def _cleanup_caches(self):
        """Clean up expired cache entries"""
        try:
            current_time = datetime.now()
            expired_symbols = []

            for symbol, last_update in self.last_cache_update.items():
                if (current_time - last_update).total_seconds() > self.config.cache_duration * 2:
                    expired_symbols.append(symbol)

            for symbol in expired_symbols:
                self.market_data_cache.pop(symbol, None)
                self.last_cache_update.pop(symbol, None)

            # Limit ultra-priority symbols to prevent overload
            if len(self.ultra_priority_symbols) > 10:
                # Keep only the most recent ones
                sorted_symbols = sorted(
                    self.ultra_priority_symbols,
                    key=lambda s: self.momentum_tracking.get(s, {}).get('last_update', datetime.min),
                    reverse=True
                )
                self.ultra_priority_symbols = set(sorted_symbols[:10])

        except Exception as e:
            self.logger.error(f"Error cleaning up caches: {e}")

    def _result_processor_loop(self):
        """Process scan results and send WebSocket updates"""
        # Create a new event loop for this thread
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)

        while self.is_running:
            try:
                # Get result from queue (blocking with timeout)
                try:
                    result = self.result_queue.get(timeout=1)
                except queue.Empty:
                    continue

                # Process the result
                self._process_scan_result(result)

                # Send WebSocket update using the thread's event loop
                loop.run_until_complete(self._send_websocket_update(result))

            except Exception as e:
                self.logger.error(f"Error processing results: {e}")

        # Clean up the event loop
        loop.close()

    def _should_scan(self) -> bool:
        """Check if scanner should perform a scan cycle"""
        if not self.config.enabled:
            return False

        # Check market hours if required
        if self.config.market_hours_only:
            # Use Central Time (Houston, TX) for market hours detection
            # Market hours: 8:30 AM - 3:00 PM CT (9:30 AM - 4:00 PM ET)
            ct_tz = pytz.timezone('US/Central')
            current_time_ct = datetime.now(ct_tz).time()

            # Market hours in Central Time
            from datetime import time as dt_time
            market_open_ct = dt_time(8, 30)  # 8:30 AM CT
            market_close_ct = dt_time(15, 0)  # 3:00 PM CT

            self.is_market_hours = market_open_ct <= current_time_ct <= market_close_ct
            if not self.is_market_hours:
                self.logger.debug(f"Market closed - Current CT time: {current_time_ct}, Market hours: {market_open_ct}-{market_close_ct}")
                return False
        
        # Check API rate limits
        with self.api_lock:
            now = datetime.now()
            if (now - self.last_api_reset).total_seconds() >= 60:
                self.api_calls_per_minute = 0
                self.last_api_reset = now
            
            if self.api_calls_per_minute >= self.config.api_rate_limit:
                return False
        
        return True

    def _perform_scan_cycle(self):
        """Perform a complete scan cycle of all symbols"""
        try:
            self.scan_count += 1
            self.last_scan_time = datetime.now()
            
            # Batch symbols for efficient scanning (OPTIMIZED for better performance)
            batch_size = self.config.max_concurrent_scans  # Use full concurrent capacity
            symbol_batches = [self.scan_symbols[i:i + batch_size]
                            for i in range(0, len(self.scan_symbols), batch_size)]
            
            # Submit scan tasks to executor
            futures = []
            for batch in symbol_batches:
                future = self.executor.submit(self._scan_symbol_batch, batch)
                futures.append(future)
            
            # Collect results
            for future in futures:
                try:
                    batch_results = future.result(timeout=30)
                    for result in batch_results:
                        if result:
                            self.result_queue.put(result)
                except Exception as e:
                    self.logger.error(f"Error getting batch results: {e}")
            
            self.logger.debug(f"Scan cycle {self.scan_count} completed")
            
        except Exception as e:
            self.logger.error(f"Error in scan cycle: {e}")

    def _perform_priority_scan_cycle(self):
        """Perform fast scan cycle of priority symbols"""
        try:
            if not hasattr(self, 'priority_symbols') or not self.priority_symbols:
                return

            self.scan_count += 1
            self.last_scan_time = datetime.now()

            # Batch priority symbols for efficient scanning (OPTIMIZED)
            batch_size = self.config.max_concurrent_scans  # Use full concurrent capacity
            symbol_batches = [self.priority_symbols[i:i + batch_size]
                            for i in range(0, len(self.priority_symbols), batch_size)]

            # Submit scan tasks to executor
            futures = []
            for batch in symbol_batches:
                future = self.executor.submit(self._scan_symbol_batch, batch)
                futures.append(future)

            # Collect results with shorter timeout for speed
            for future in futures:
                try:
                    results = future.result(timeout=20)  # Reduced timeout for speed
                    for result in results:
                        if result:
                            self.result_queue.put(result)
                except Exception as e:
                    self.logger.error(f"Priority scan batch failed: {e}")

            self.logger.debug(f"[PRIORITY] Fast scan completed: {len(self.priority_symbols)} symbols")

        except Exception as e:
            self.logger.error(f"Priority scan cycle failed: {e}")

    def _perform_regular_scan_cycle(self):
        """Perform regular scan cycle of non-priority symbols"""
        try:
            if not hasattr(self, 'regular_symbols') or not self.regular_symbols:
                return

            # Batch regular symbols for efficient scanning
            batch_size = max(1, self.config.max_concurrent_scans // 2)  # Use half capacity for regular symbols
            symbol_batches = [self.regular_symbols[i:i + batch_size]
                            for i in range(0, len(self.regular_symbols), batch_size)]

            # Submit scan tasks to executor
            futures = []
            for batch in symbol_batches:
                future = self.executor.submit(self._scan_symbol_batch, batch)
                futures.append(future)

            # Collect results
            for future in futures:
                try:
                    results = future.result(timeout=30)  # Standard timeout
                    for result in results:
                        if result:
                            self.result_queue.put(result)
                except Exception as e:
                    self.logger.error(f"Regular scan batch failed: {e}")

            self.logger.debug(f"[REGULAR] Scan completed: {len(self.regular_symbols)} symbols")

        except Exception as e:
            self.logger.error(f"Regular scan cycle failed: {e}")

    def _scan_symbol_batch(self, symbols: List[str]) -> List[Optional[ScannerResult]]:
        """Scan a batch of symbols for Lee Method patterns"""
        results = []
        
        for symbol in symbols:
            try:
                result = self._scan_single_symbol(symbol)
                results.append(result)
                
                # Update API call counter
                with self.api_lock:
                    self.api_calls_per_minute += 1
                
            except Exception as e:
                self.logger.error(f"Error scanning {symbol}: {e}")
                results.append(None)
        
        return results

    def _scan_single_symbol(self, symbol: str) -> Optional[ScannerResult]:
        """Scan a single symbol for Lee Method pattern"""
        try:
            # Create a new event loop for this thread if needed
            try:
                loop = asyncio.get_event_loop()
            except RuntimeError:
                # No event loop in this thread, create one
                loop = asyncio.new_event_loop()
                asyncio.set_event_loop(loop)

            try:
                # Run async operations in the thread's event loop
                signal = loop.run_until_complete(self.lee_scanner.scan_symbol(symbol))

                if not signal or signal.confidence < self.config.min_confidence:
                    return None

                # Get current market data
                market_data = loop.run_until_complete(self._get_market_data(symbol))

                if not market_data:
                    return None

                # Create scanner result
                result = ScannerResult(
                    symbol=symbol,
                    price=market_data.get('price', 0.0),
                    change=market_data.get('change', 0.0),
                    change_percent=market_data.get('change_percent', 0.0),
                    pattern_found=True,
                    pattern_type='lee_method_ttm_squeeze',
                    confidence=signal.confidence,
                    signal_strength=signal.strength if signal.strength else 'MODERATE',
                    timestamp=datetime.now().isoformat(),
                    histogram_current=signal.histogram_current,
                    ema5_trend=signal.ema5_uptrend,
                    ema8_trend=signal.ema8_uptrend,
                    momentum_trend=signal.momentum_uptrend,
                    squeeze_active=signal.squeeze_active,
                    target_price=signal.target_price,
                    stop_loss=signal.stop_loss
                )

                # Generate ultra-responsive alert for this pattern
                try:
                    alert_start_time = time_module.time()

                    pattern_data = {
                        'signal_type': signal.signal_type,
                        'confidence': signal.confidence,
                        'signal_strength': signal.strength,
                        'histogram_current': signal.histogram_current,
                        'histogram_previous': getattr(signal, 'histogram_previous', 0.0),
                        'improvement_magnitude': abs(signal.histogram_current - getattr(signal, 'histogram_previous', 0.0)),
                        'ema8_rising': signal.ema8_uptrend,
                        'ema21_rising': getattr(signal, 'ema21_uptrend', False),
                        'squeeze_active': signal.squeeze_active,
                        'pattern_details': getattr(signal, 'pattern_details', {})
                    }

                    # Generate alert asynchronously (non-blocking)
                    alert = loop.run_until_complete(
                        self.alert_manager.generate_ttm_squeeze_alert(symbol, pattern_data, market_data)
                    )

                    # Record alert delivery performance
                    alert_duration = time_module.time() - alert_start_time
                    self.performance_monitor.record_metric(
                        PerformanceMetric.ALERT_DELIVERY_TIME, alert_duration, symbol
                    )

                    if alert_duration > 1.0:  # Alert took longer than 1 second
                        self.logger.warning(f"[PERFORMANCE] Slow alert delivery for {symbol}: {alert_duration:.2f}s")

                except Exception as e:
                    self.logger.error(f"Error generating alert for {symbol}: {e}")

                return result

            except Exception as e:
                self.logger.error(f"Error in async operations for {symbol}: {e}")
                return None

        except Exception as e:
            self.logger.error(f"Error scanning symbol {symbol}: {e}")
            return None

    async def _get_market_data(self, symbol: str) -> Optional[Dict[str, Any]]:
        """Get current market data for symbol with fallback mechanisms"""
        try:
            # Use the integrated market engine with multiple data sources
            quote = await self.market_engine.get_quote(symbol)
            if quote:
                return {
                    'price': quote.price,
                    'change': quote.change,
                    'change_percent': quote.change_percent,
                    'volume': quote.volume,
                    'timestamp': quote.timestamp.isoformat()
                }

            # Try FMP API directly as final attempt for REAL market data
            fmp_data = await self._get_fmp_fallback_data(symbol)
            if fmp_data:
                return fmp_data

            # CRITICAL SAFETY: All real data sources failed - no synthetic data
            self.logger.error(f"CRITICAL: All market data sources failed for {symbol} - SCANNER CANNOT PROCEED")
            return None

        except Exception as e:
            self.logger.error(f"CRITICAL: Market data error for {symbol}: {e} - SCANNER CANNOT PROCEED")
            return None

    async def _get_fmp_fallback_data(self, symbol: str) -> Optional[Dict[str, Any]]:
        """Fallback method to get basic market data from FMP API"""
        try:
            import aiohttp

            # Use FMP API key from settings
            fmp_api_key = getattr(settings, 'FMP_API_KEY', 'K63wnAbUDHNPICjzeq3JcEG1vi8Q2oz7')
            url = f"https://financialmodelingprep.com/api/v3/quote/{symbol}"
            params = {"apikey": fmp_api_key}

            async with aiohttp.ClientSession() as session:
                async with session.get(url, params=params, timeout=aiohttp.ClientTimeout(total=5)) as response:
                    if response.status == 200:
                        data = await response.json()
                        if data and len(data) > 0:
                            quote_data = data[0]
                            return {
                                'price': float(quote_data.get('price', 0)),
                                'change': float(quote_data.get('change', 0)),
                                'change_percent': float(quote_data.get('changesPercentage', 0)),
                                'volume': int(quote_data.get('volume', 0)),
                                'timestamp': datetime.now().isoformat()
                            }

            return None

        except Exception as e:
            self.logger.error(f"FMP fallback error for {symbol}: {e}")
            return None

    def _process_scan_result(self, result: ScannerResult):
        """Process and store scan result"""
        try:
            # Update active results
            self.active_results[result.symbol] = result
            
            # Clean up old results (older than 1 hour)
            cutoff_time = datetime.now() - timedelta(hours=1)
            expired_symbols = []
            
            for symbol, stored_result in self.active_results.items():
                result_time = datetime.fromisoformat(stored_result.timestamp)
                if result_time < cutoff_time:
                    expired_symbols.append(symbol)
            
            for symbol in expired_symbols:
                del self.active_results[symbol]
                
        except Exception as e:
            self.logger.error(f"Error processing scan result: {e}")

    async def _send_websocket_update(self, result: ScannerResult):
        """Send real-time update to WebSocket connections"""
        try:
            if not self.websocket_connections:
                return

            # Convert result to dict and ensure JSON serializable
            result_dict = asdict(result)
            result_dict['pattern_found'] = bool(result_dict['pattern_found'])
            result_dict['ema5_trend'] = bool(result_dict['ema5_trend'])
            result_dict['ema8_trend'] = bool(result_dict['ema8_trend'])
            result_dict['momentum_trend'] = bool(result_dict['momentum_trend'])
            result_dict['squeeze_active'] = bool(result_dict['squeeze_active'])

            update_data = {
                'type': 'scanner_update',
                'data': result_dict
            }

            # Send to all connected WebSocket clients
            disconnected = set()
            for websocket in self.websocket_connections:
                try:
                    await websocket.send(json.dumps(update_data))
                except Exception:
                    disconnected.add(websocket)

            # Remove disconnected clients
            self.websocket_connections -= disconnected

        except Exception as e:
            self.logger.error(f"Error sending WebSocket update: {e}")

    async def _load_scanner_config(self):
        """Load scanner configuration from settings"""
        try:
            # Load from settings or use defaults
            self.config.enabled = getattr(settings, 'SCANNER_ENABLED', True)
            self.config.scan_interval = getattr(settings, 'SCANNER_INTERVAL', 30)
            self.config.market_hours_only = getattr(settings, 'SCANNER_MARKET_HOURS_ONLY', True)
            self.config.min_confidence = getattr(settings, 'SCANNER_MIN_CONFIDENCE', 0.6)
            self.config.max_concurrent_scans = getattr(settings, 'SCANNER_MAX_CONCURRENT', 10)
            self.config.api_rate_limit = getattr(settings, 'SCANNER_API_RATE_LIMIT', 100)
            
        except Exception as e:
            self.logger.error(f"Error loading scanner config: {e}")

    # Public API methods
    
    async def get_active_results(self) -> List[Dict[str, Any]]:
        """Get current active scanner results"""
        try:
            results = []
            for result in self.active_results.values():
                # Convert dataclass to dict and ensure JSON serializable
                result_dict = asdict(result)

                # Convert boolean fields to ensure JSON compatibility
                result_dict['pattern_found'] = bool(result_dict['pattern_found'])
                result_dict['ema5_trend'] = bool(result_dict['ema5_trend'])
                result_dict['ema8_trend'] = bool(result_dict['ema8_trend'])
                result_dict['momentum_trend'] = bool(result_dict['momentum_trend'])
                result_dict['squeeze_active'] = bool(result_dict['squeeze_active'])

                results.append(result_dict)

            # Sort by confidence descending
            results.sort(key=lambda x: x['confidence'], reverse=True)
            return results

        except Exception as e:
            self.logger.error(f"Error getting active results: {e}")
            return []

    async def update_config(self, config_updates: Dict[str, Any]) -> bool:
        """Update scanner configuration"""
        try:
            for key, value in config_updates.items():
                if hasattr(self.config, key):
                    setattr(self.config, key, value)
            
            self.logger.info(f"Scanner configuration updated: {config_updates}")
            return True
            
        except Exception as e:
            self.logger.error(f"Error updating scanner config: {e}")
            return False

    def add_websocket_connection(self, websocket):
        """Add WebSocket connection for real-time updates"""
        self.websocket_connections.add(websocket)
        # Also add to alert manager for instant alert delivery
        self.alert_manager.add_websocket_connection(websocket)

    def remove_websocket_connection(self, websocket):
        """Remove WebSocket connection"""
        self.websocket_connections.discard(websocket)
        # Also remove from alert manager
        self.alert_manager.remove_websocket_connection(websocket)

    def get_scanner_status(self) -> Dict[str, Any]:
        """Get current scanner status with ultra-responsive performance metrics"""
        current_time = datetime.now()

        # Clean up caches periodically
        self._cleanup_caches()

        # Calculate scanning frequencies
        ultra_priority_frequency = f"Every {self.config.ultra_priority_scan_interval}s" if hasattr(self.config, 'ultra_priority_scan_interval') else "N/A"
        priority_frequency = f"Every {self.config.priority_scan_interval}s" if hasattr(self.config, 'priority_scan_interval') else "N/A"
        regular_frequency = f"Every {self.config.scan_interval}s"

        # Calculate performance metrics
        symbols_per_minute = 0
        if self.last_scan_time and self.scan_count > 0:
            time_diff = (current_time - self.last_scan_time).total_seconds()
            if time_diff > 0:
                symbols_per_minute = (len(getattr(self, 'scan_symbols', [])) * 60) / max(time_diff, 1)

        return {
            'running': self.is_running,
            'market_hours': self.is_market_hours,
            'scan_count': self.scan_count,
            'last_scan_time': self.last_scan_time.isoformat() if self.last_scan_time else None,
            'active_results_count': len(self.active_results),
            'symbols_monitored': len(getattr(self, 'scan_symbols', [])),
            'priority_symbols': len(getattr(self, 'priority_symbols', [])),
            'ultra_priority_symbols': len(getattr(self, 'ultra_priority_symbols', set())),
            'regular_symbols': len(getattr(self, 'regular_symbols', [])),
            'api_calls_per_minute': self.api_calls_per_minute,
            'cache_status': {
                'cached_symbols': len(self.market_data_cache),
                'cache_hit_ratio': 'N/A',  # Could be calculated if needed
                'cache_duration': f"{self.config.cache_duration}s"
            },
            'performance': {
                'ultra_priority_scan_frequency': ultra_priority_frequency,
                'priority_scan_frequency': priority_frequency,
                'regular_scan_frequency': regular_frequency,
                'symbols_per_minute': round(symbols_per_minute, 1),
                'concurrent_scans': self.config.max_concurrent_scans,
                'parallel_processing': self.config.enable_parallel_processing,
                'worker_threads': self.config.max_worker_threads if self.config.enable_parallel_processing else 0,
                'api_rate_limit': self.config.api_rate_limit,
                'optimization_status': 'ULTRA-RESPONSIVE - Multi-tier scanning enabled'
            },
            'alert_system': self.alert_manager.get_alert_status() if hasattr(self, 'alert_manager') else {},
            'performance_monitoring': self.performance_monitor.get_performance_status() if hasattr(self, 'performance_monitor') else {},
            'config': asdict(self.config)
        }

    def get_status(self) -> Dict[str, Any]:
        """Get current scanner status - alias for get_scanner_status"""
        return self.get_scanner_status()
