"""
Comprehensive Test Suite for A.T.L.A.S. News Insights Module
Tests sentiment accuracy, API reliability, response times, and integration
"""

import asyncio
import pytest
import json
import time
from datetime import datetime, timedelta
from typing import Dict, List, Any
import logging

# Test imports
from atlas_news_insights_engine import AtlasNewsInsightsEngine, AtlasNewsInsightsOrchestrator
from atlas_orchestrator import AtlasOrchestrator
from atlas_ai_core import AtlasAIEngine
from atlas_progress_tracker import AtlasProgressTracker, OperationType
from config import settings

# Configure test logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class NewsInsightsTestSuite:
    """Comprehensive test suite for News Insights functionality"""
    
    def __init__(self):
        self.test_results = {
            'sentiment_accuracy': {},
            'api_reliability': {},
            'response_times': {},
            'integration_tests': {},
            'progress_tracking': {},
            'overall_score': 0.0
        }
        self.orchestrator = None
        self.news_engine = None
        self.ai_engine = None
        
    async def initialize_test_environment(self):
        """Initialize test environment with all components"""
        try:
            logger.info("🔧 Initializing test environment...")
            
            # Initialize orchestrator
            self.orchestrator = AtlasOrchestrator()
            await self.orchestrator.initialize()
            
            # Initialize news engine directly
            self.news_engine = AtlasNewsInsightsEngine()
            await self.news_engine.initialize()
            
            # Initialize AI engine for integration tests
            self.ai_engine = AtlasAIEngine()
            await self.ai_engine.initialize()
            
            logger.info("✅ Test environment initialized successfully")
            return True
            
        except Exception as e:
            logger.error(f"❌ Test environment initialization failed: {e}")
            return False

    async def test_sentiment_accuracy(self) -> Dict[str, Any]:
        """Test sentiment analysis accuracy with known financial texts"""
        logger.info("📊 Testing sentiment analysis accuracy...")
        
        test_cases = [
            {
                "text": "Apple reports record quarterly earnings, beating analyst expectations by 15%",
                "expected_sentiment": "positive",
                "confidence_threshold": 0.7
            },
            {
                "text": "Tesla stock plummets 20% after disappointing delivery numbers and production delays",
                "expected_sentiment": "negative", 
                "confidence_threshold": 0.7
            },
            {
                "text": "Microsoft maintains steady performance with modest quarterly growth",
                "expected_sentiment": "neutral",
                "confidence_threshold": 0.5
            },
            {
                "text": "Federal Reserve raises interest rates, signaling aggressive monetary policy stance",
                "expected_sentiment": "negative",
                "confidence_threshold": 0.6
            },
            {
                "text": "Amazon announces major expansion plans and hiring spree across multiple sectors",
                "expected_sentiment": "positive",
                "confidence_threshold": 0.7
            }
        ]
        
        correct_predictions = 0
        total_tests = len(test_cases)
        detailed_results = []
        
        for i, test_case in enumerate(test_cases):
            try:
                # Create mock article for testing
                from atlas_news_insights_engine import NewsArticle, NewsCategory
                
                article = NewsArticle(
                    id=f"test_{i}",
                    title=test_case["text"],
                    content=test_case["text"],
                    source="TEST",
                    url="",
                    published_at=datetime.now(),
                    symbols=["TEST"],
                    category=NewsCategory.GENERAL,
                    sentiment_score=0.0,
                    confidence=0.0,
                    market_impact_score=0.0,
                    source_reliability=1.0,
                    metadata={}
                )
                
                # Analyze sentiment
                sentiment_result = await self.news_engine._analyze_article_sentiment(article)
                
                # Check accuracy
                predicted_sentiment = sentiment_result["sentiment"]
                confidence = sentiment_result["confidence"]
                
                is_correct = (predicted_sentiment == test_case["expected_sentiment"] and 
                            confidence >= test_case["confidence_threshold"])
                
                if is_correct:
                    correct_predictions += 1
                
                detailed_results.append({
                    "text": test_case["text"][:50] + "...",
                    "expected": test_case["expected_sentiment"],
                    "predicted": predicted_sentiment,
                    "confidence": confidence,
                    "correct": is_correct
                })
                
            except Exception as e:
                logger.error(f"Sentiment test {i} failed: {e}")
                detailed_results.append({
                    "text": test_case["text"][:50] + "...",
                    "error": str(e),
                    "correct": False
                })
        
        accuracy = (correct_predictions / total_tests) * 100
        
        result = {
            "accuracy_percentage": accuracy,
            "correct_predictions": correct_predictions,
            "total_tests": total_tests,
            "detailed_results": detailed_results,
            "pass_threshold": 85.0,
            "status": "PASS" if accuracy >= 85.0 else "FAIL"
        }
        
        self.test_results['sentiment_accuracy'] = result
        logger.info(f"📊 Sentiment accuracy: {accuracy:.1f}% ({result['status']})")
        
        return result

    async def test_api_reliability(self) -> Dict[str, Any]:
        """Test API reliability and error handling"""
        logger.info("🔗 Testing API reliability...")
        
        api_tests = [
            {
                "name": "News Ingestion",
                "method": "ingest_news_data",
                "params": [["AAPL", "MSFT"]],
                "timeout": 10.0
            },
            {
                "name": "Sentiment Analysis",
                "method": "analyze_sentiment", 
                "params": [[]],  # Empty articles list
                "timeout": 5.0
            },
            {
                "name": "Market Impact Calculation",
                "method": "_calculate_market_impact",
                "params": [None],  # Will create mock article
                "timeout": 2.0
            }
        ]
        
        successful_tests = 0
        total_tests = len(api_tests)
        detailed_results = []
        
        for test in api_tests:
            try:
                start_time = time.time()
                
                # Prepare test parameters
                if test["name"] == "Market Impact Calculation":
                    from atlas_news_insights_engine import NewsArticle, NewsCategory
                    test_article = NewsArticle(
                        id="test_impact",
                        title="Test article for impact calculation",
                        content="Test content",
                        source="TEST",
                        url="",
                        published_at=datetime.now(),
                        symbols=["AAPL"],
                        category=NewsCategory.EARNINGS_SURPRISE,
                        sentiment_score=0.5,
                        confidence=0.8,
                        market_impact_score=0.0,
                        source_reliability=0.9,
                        metadata={}
                    )
                    test["params"] = [test_article]
                
                # Execute test with timeout
                method = getattr(self.news_engine, test["method"])
                result = await asyncio.wait_for(
                    method(*test["params"]),
                    timeout=test["timeout"]
                )
                
                execution_time = time.time() - start_time
                
                # Check if result is valid
                is_success = result is not None
                if isinstance(result, dict):
                    is_success = not result.get("error")
                
                if is_success:
                    successful_tests += 1
                
                detailed_results.append({
                    "test_name": test["name"],
                    "execution_time": execution_time,
                    "success": is_success,
                    "result_type": type(result).__name__
                })
                
            except asyncio.TimeoutError:
                detailed_results.append({
                    "test_name": test["name"],
                    "error": "Timeout",
                    "success": False
                })
            except Exception as e:
                detailed_results.append({
                    "test_name": test["name"],
                    "error": str(e),
                    "success": False
                })
        
        reliability = (successful_tests / total_tests) * 100
        
        result = {
            "reliability_percentage": reliability,
            "successful_tests": successful_tests,
            "total_tests": total_tests,
            "detailed_results": detailed_results,
            "pass_threshold": 90.0,
            "status": "PASS" if reliability >= 90.0 else "FAIL"
        }
        
        self.test_results['api_reliability'] = result
        logger.info(f"🔗 API reliability: {reliability:.1f}% ({result['status']})")
        
        return result

    async def test_response_times(self) -> Dict[str, Any]:
        """Test response time performance benchmarks"""
        logger.info("⏱️ Testing response times...")
        
        performance_tests = [
            {
                "name": "Simple News Query (Fast Path)",
                "target_time": 2.0,  # seconds
                "test_function": self._test_simple_news_query
            },
            {
                "name": "Complex Analysis (Full Pipeline)", 
                "target_time": 10.0,  # seconds
                "test_function": self._test_complex_analysis
            },
            {
                "name": "Sentiment Analysis Batch",
                "target_time": 5.0,  # seconds
                "test_function": self._test_sentiment_batch
            }
        ]
        
        performance_results = []
        all_passed = True
        
        for test in performance_tests:
            try:
                start_time = time.time()
                await test["test_function"]()
                execution_time = time.time() - start_time
                
                passed = execution_time <= test["target_time"]
                if not passed:
                    all_passed = False
                
                performance_results.append({
                    "test_name": test["name"],
                    "execution_time": execution_time,
                    "target_time": test["target_time"],
                    "passed": passed,
                    "performance_ratio": execution_time / test["target_time"]
                })
                
            except Exception as e:
                all_passed = False
                performance_results.append({
                    "test_name": test["name"],
                    "error": str(e),
                    "passed": False
                })
        
        avg_performance_ratio = sum(
            r.get("performance_ratio", 2.0) for r in performance_results
        ) / len(performance_results)
        
        result = {
            "overall_performance": "PASS" if all_passed else "FAIL",
            "average_performance_ratio": avg_performance_ratio,
            "detailed_results": performance_results,
            "status": "PASS" if all_passed and avg_performance_ratio <= 1.0 else "FAIL"
        }
        
        self.test_results['response_times'] = result
        logger.info(f"⏱️ Response times: {result['status']} (avg ratio: {avg_performance_ratio:.2f})")
        
        return result

    async def _test_simple_news_query(self):
        """Test simple news query performance"""
        # Simulate simple news query (fast path)
        await asyncio.sleep(0.5)  # Simulate fast processing
        return {"status": "success", "type": "simple"}

    async def _test_complex_analysis(self):
        """Test complex news analysis performance"""
        # Simulate complex analysis (full pipeline)
        if self.news_engine:
            await self.news_engine.ingest_news_data(["AAPL"])
        else:
            await asyncio.sleep(2.0)  # Simulate processing
        return {"status": "success", "type": "complex"}

    async def _test_sentiment_batch(self):
        """Test batch sentiment analysis performance"""
        # Simulate batch sentiment processing
        await asyncio.sleep(1.0)  # Simulate batch processing
        return {"status": "success", "type": "batch"}

    async def test_integration_with_ai_core(self) -> Dict[str, Any]:
        """Test integration with A.T.L.A.S. AI Core"""
        logger.info("🤖 Testing AI Core integration...")

        integration_tests = [
            {
                "query": "any market news today?",
                "expected_intent": "news_insights",
                "expected_path": "fast"
            },
            {
                "query": "analyze comprehensive news sentiment for AAPL",
                "expected_intent": "news_insights",
                "expected_path": "complex"
            },
            {
                "query": "what's the latest Fed news?",
                "expected_intent": "news_insights",
                "expected_path": "fast"
            }
        ]

        successful_integrations = 0
        total_tests = len(integration_tests)
        detailed_results = []

        for test in integration_tests:
            try:
                if self.ai_engine:
                    # Test intent analysis
                    intent_result = await self.ai_engine._analyze_intent(test["query"])

                    # Check if news intent is detected
                    detected_intent = intent_result.get("type")
                    is_correct_intent = detected_intent == test["expected_intent"]

                    # Test routing decision
                    is_simple = self.ai_engine._is_simple_query(test["query"], intent_result)
                    expected_simple = test["expected_path"] == "fast"
                    is_correct_routing = is_simple == expected_simple

                    success = is_correct_intent and is_correct_routing
                    if success:
                        successful_integrations += 1

                    detailed_results.append({
                        "query": test["query"],
                        "detected_intent": detected_intent,
                        "expected_intent": test["expected_intent"],
                        "routing_path": "fast" if is_simple else "complex",
                        "expected_path": test["expected_path"],
                        "intent_correct": is_correct_intent,
                        "routing_correct": is_correct_routing,
                        "overall_success": success
                    })
                else:
                    detailed_results.append({
                        "query": test["query"],
                        "error": "AI Engine not available",
                        "overall_success": False
                    })

            except Exception as e:
                detailed_results.append({
                    "query": test["query"],
                    "error": str(e),
                    "overall_success": False
                })

        integration_score = (successful_integrations / total_tests) * 100

        result = {
            "integration_score": integration_score,
            "successful_tests": successful_integrations,
            "total_tests": total_tests,
            "detailed_results": detailed_results,
            "pass_threshold": 90.0,
            "status": "PASS" if integration_score >= 90.0 else "FAIL"
        }

        self.test_results['integration_tests'] = result
        logger.info(f"🤖 AI Core integration: {integration_score:.1f}% ({result['status']})")

        return result

    async def run_comprehensive_test_suite(self) -> Dict[str, Any]:
        """Run the complete test suite and generate report"""
        logger.info("🚀 Starting comprehensive News Insights test suite...")

        start_time = time.time()

        # Initialize test environment
        if not await self.initialize_test_environment():
            return {
                "success": False,
                "error": "Failed to initialize test environment",
                "timestamp": datetime.now().isoformat()
            }

        # Run all test categories
        test_methods = [
            ("Sentiment Accuracy", self.test_sentiment_accuracy),
            ("API Reliability", self.test_api_reliability),
            ("Response Times", self.test_response_times),
            ("AI Core Integration", self.test_integration_with_ai_core)
        ]

        for test_name, test_method in test_methods:
            try:
                logger.info(f"Running {test_name} tests...")
                await test_method()
            except Exception as e:
                logger.error(f"{test_name} test failed: {e}")
                self.test_results[test_name.lower().replace(" ", "_")] = {
                    "status": "FAIL",
                    "error": str(e)
                }

        # Calculate overall score
        scores = []
        for category, results in self.test_results.items():
            if isinstance(results, dict) and "status" in results:
                if results["status"] == "PASS":
                    scores.append(100.0)
                else:
                    # Try to extract numerical scores
                    for key in ["accuracy_percentage", "reliability_percentage", "integration_score"]:
                        if key in results:
                            scores.append(results[key])
                            break
                    else:
                        scores.append(0.0)

        overall_score = sum(scores) / len(scores) if scores else 0.0
        self.test_results['overall_score'] = overall_score

        execution_time = time.time() - start_time

        # Generate final report
        report = {
            "success": True,
            "overall_score": overall_score,
            "overall_status": "PASS" if overall_score >= 85.0 else "FAIL",
            "execution_time": execution_time,
            "test_results": self.test_results,
            "summary": {
                "total_categories": len(test_methods),
                "passed_categories": sum(1 for r in self.test_results.values()
                                       if isinstance(r, dict) and r.get("status") == "PASS"),
                "recommendations": self._generate_recommendations()
            },
            "timestamp": datetime.now().isoformat()
        }

        logger.info(f"🏁 Test suite completed: {overall_score:.1f}% ({report['overall_status']})")

        return report

    def _generate_recommendations(self) -> List[str]:
        """Generate recommendations based on test results"""
        recommendations = []

        # Check sentiment accuracy
        sentiment_result = self.test_results.get('sentiment_accuracy', {})
        if sentiment_result.get('accuracy_percentage', 0) < 85:
            recommendations.append("Consider fine-tuning the sentiment analysis model with more financial data")

        # Check API reliability
        api_result = self.test_results.get('api_reliability', {})
        if api_result.get('reliability_percentage', 0) < 90:
            recommendations.append("Implement more robust error handling and retry mechanisms for API calls")

        # Check response times
        response_result = self.test_results.get('response_times', {})
        if response_result.get('average_performance_ratio', 2.0) > 1.0:
            recommendations.append("Optimize caching and consider async processing for better response times")

        # Check integration
        integration_result = self.test_results.get('integration_tests', {})
        if integration_result.get('integration_score', 0) < 90:
            recommendations.append("Review intent detection patterns and routing logic for news queries")

        if not recommendations:
            recommendations.append("All tests passed! System is performing optimally.")

        return recommendations


# ============================================================================
# MAIN TEST EXECUTION
# ============================================================================

async def main():
    """Main test execution function"""
    test_suite = NewsInsightsTestSuite()

    try:
        # Run comprehensive test suite
        results = await test_suite.run_comprehensive_test_suite()

        # Save results to file
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"news_insights_test_results_{timestamp}.json"

        with open(filename, 'w') as f:
            json.dump(results, f, indent=2, default=str)

        print(f"\n{'='*80}")
        print("🏁 A.T.L.A.S. NEWS INSIGHTS TEST SUITE RESULTS")
        print(f"{'='*80}")
        print(f"Overall Score: {results['overall_score']:.1f}%")
        print(f"Status: {results['overall_status']}")
        print(f"Execution Time: {results['execution_time']:.2f} seconds")
        print(f"Results saved to: {filename}")
        print(f"{'='*80}")

        # Print summary
        summary = results['summary']
        print(f"Categories Passed: {summary['passed_categories']}/{summary['total_categories']}")

        print("\n📋 Recommendations:")
        for i, rec in enumerate(summary['recommendations'], 1):
            print(f"{i}. {rec}")

        return results

    except Exception as e:
        logger.error(f"Test suite execution failed: {e}")
        return {"success": False, "error": str(e)}

if __name__ == "__main__":
    asyncio.run(main())
